# Test variable assignment with nested functions

from math import basic

# Define variables
x = 10
y = 5

# Test variable assignment with nested functions
print Testing variable assignment with nested functions:

# Test basic variable assignment
result1 = $basic.add($x, $y)
print result1 should be 15: $result1

# Test variable assignment with nested functions
result2 = $basic.add($basic.multiply(2, 3), $basic.subtract(10, 5))
print result2 should be 11: $result2

# Test using variables in if statements
if $result1 == 15:
    print result1 equals 15 - This should display

if $result2 > 10:
    print result2 is greater than 10 - This should display

if $result1 < $result2:
    print result1 is less than result2 - This should NOT display

# This is not an else block (not supported in this syntax)
print result1 is NOT less than result2 - This should display
