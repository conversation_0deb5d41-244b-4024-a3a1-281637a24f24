# Test if statements with nested functions and variable assignment

from math import basic

# Define variables
x = 10
y = 5

# Test variable assignment with nested functions
print Testing variable assignment with nested functions:

# Test basic variable assignment
result1 = $basic.multiply($x, 2)
print result1 should be 20: $result1

# Test variable assignment with nested functions
result2 = $basic.add($y, 10)
print result2 should be 15: $result2

# Test if statements with variables
print Testing if statements with variables:

# Test equal comparison
if $basic.add($x, $y) == 15:
    print $x + $y equals 15 - This should display

# Test greater than comparison
if $result1 > $result2:
    print $x * 2 is greater than $y + 10 - This should display

# Test less than comparison
result3 = $basic.subtract($x, $y)
result4 = $basic.multiply($y, 2)
if $result3 < $result4:
    print $x - $y is less than $y * 2 - This should display

# Test condition that should be false
if $basic.add($x, $y) == 20:
    print This should NOT display

# This is not an else block (not supported in this syntax)
print $x + $y does not equal 20 - This should display

# Test nested functions in both sides of comparison
if $basic.multiply($x, 2) == $basic.add($x, 10):
    print $x * 2 equals $x + 10 - This should display
