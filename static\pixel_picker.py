import sys
import pya<PERSON>gui
import keyboard
import pyperclip
from PyQt5.QtWidgets import (QApplication, QWidget, QVBoxLayout, QPushButton, 
                            QLabel, QCheckBox, QDialog)
from PyQt5.QtCore import Qt
from PyQt5 import QtGui
from settings import themes as themes

class HelpWindow(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Pixel Picker Help")
        self.setGeometry(150, 150, 400, 300)
        self.setStyleSheet(themes.get_stylesheet())
        
        # Ensure it's a separate window
        self.setWindowFlags(
            Qt.Window |  # Make it a window # type: ignore
            Qt.WindowCloseButtonHint |  # Add close button # type: ignore
            Qt.WindowStaysOnTopHint  # Keep on top # type: ignore
        )
        
        layout = QVBoxLayout()
        
        # Title
        title = QLabel("How to Use Pixel Picker", self)
        title.setFont(QtGui.QFont("Arial", 12, QtGui.QFont.Bold))
        layout.addWidget(title)
        layout.addSpacing(10)
        
        # Modes section
        modes_title = QLabel("Modes:", self)
        modes_title.setFont(QtGui.QFont("Arial", 10, QtGui.QFont.Bold))
        layout.addWidget(modes_title)
        
        layout.addWidget(QLabel("• Coordinates Mode: Shows and copies mouse position (x, y)", self))
        layout.addWidget(QLabel("• RGB Mode: Shows and copies pixel color values (r, g, b)", self))
        layout.addSpacing(10)
        
        # Controls section
        controls_title = QLabel("Controls:", self)
        controls_title.setFont(QtGui.QFont("Arial", 10, QtGui.QFont.Bold))
        layout.addWidget(controls_title)
        
        layout.addWidget(QLabel("Toggle between modes:", self))
        layout.addWidget(QLabel("• Click the 'Toggle Mode' button", self))
        layout.addWidget(QLabel("• Press Alt+T", self))
        layout.addSpacing(5)
        
        layout.addWidget(QLabel("Capture position/color:", self))
        layout.addWidget(QLabel("• Press Alt+C", self))
        layout.addSpacing(5)
        
        layout.addWidget(QLabel("Copy to clipboard:", self))
        layout.addWidget(QLabel("• Click the 'Copy' button", self))
        layout.addWidget(QLabel("• Enable 'Auto Copy' to copy automatically when capturing", self))
        
        # Close button
        close_button = QPushButton("Close", self)
        close_button.clicked.connect(self.close) # type: ignore[call-arg]
        layout.addSpacing(10)
        layout.addWidget(close_button)
        
        self.setLayout(layout)

class PixelPicker(QWidget):
    def __init__(self) -> None:
        super().__init__()

        # Initial states
        self.mode: str = 'coordinates'
        self.auto_copy: bool = False
        self.last_position: tuple[int, int] = (0, 0)
        self.last_rgb: tuple[int, int, int] = (0, 0, 0)

        # Window setup
        self.setWindowTitle("Pixel Picker")
        self.setGeometry(100, 100, 375, 300)
        self.setStyleSheet(themes.get_stylesheet())
        self.setWindowFlags(Qt.WindowStaysOnTopHint) # type:ignore[arg-type]
        self.setFixedSize(375, 300)  # Fixed size to prevent resizing

        # Layout and widgets
        layout = QVBoxLayout()

        # Current position and value display
        self.position_label = QLabel("Position: (0, 0)", self)
        self.position_label.setFont(QtGui.QFont("Arial", 12))
        layout.addWidget(self.position_label)

        self.value_label = QLabel("RGB: (0, 0, 0)", self)
        self.value_label.setFont(QtGui.QFont("Arial", 12))
        layout.addWidget(self.value_label)

        # Status label
        self.status_label = QLabel("Mode: Coordinates", self)
        self.status_label.setFont(QtGui.QFont("Arial", 10, QtGui.QFont.Bold))
        layout.addWidget(self.status_label)

        # Copy button
        self.copy_button = QPushButton("Copy", self)
        self.copy_button.clicked.connect(self.copy_to_clipboard)
        layout.addWidget(self.copy_button)

        # Toggle mode button
        self.toggle_button = QPushButton("Toggle Mode", self)
        self.toggle_button.clicked.connect(self.toggle_mode)
        layout.addWidget(self.toggle_button)
        
        # Auto-copy checkbox
        self.auto_copy_checkbox = QCheckBox("Auto Copy", self)
        self.auto_copy_checkbox.stateChanged.connect(self.toggle_auto_copy)
        layout.addWidget(self.auto_copy_checkbox)
        
        # Help button
        self.help_button = QPushButton("Help", self)
        self.help_button.clicked.connect(self.show_help)
        layout.addWidget(self.help_button)

        self.setLayout(layout)

        # Set up global hotkeys
        keyboard.add_hotkey('alt+c', self.capture_coordinates_and_rgb)
        keyboard.add_hotkey('alt+t', self.toggle_mode)

    def show_help(self) -> None:
        """Show the help window"""
        help_window = HelpWindow(self)
        help_window.exec_()

    def toggle_auto_copy(self, state: int) -> None:
        """Toggle auto-copy mode"""
        self.auto_copy = state == Qt.Checked # type: ignore[assignment]

    def update_position(self) -> None:
        """Update the current mouse position display"""
        x, y = pyautogui.position()
        self.last_position = (x, y)
        self.position_label.setText(f"Position: ({x}, {y})")

        self.last_rgb = pyautogui.pixel(x, y)
        self.value_label.setText(f"RGB: ({self.last_rgb[0]}, {self.last_rgb[1]}, {self.last_rgb[2]})")

    def capture_coordinates_and_rgb(self) -> None:
        """Capture current coordinates and RGB values and optionally copy to clipboard"""
        x, y = pyautogui.position()
        self.last_position = (x, y)
        self.position_label.setText(f"Position: ({x}, {y})")
        try:
            rgb = pyautogui.pixel(*self.last_position)
            self.last_rgb = rgb
            self.value_label.setText(f"RGB: ({rgb[0]}, {rgb[1]}, {rgb[2]})")
        except:
            self.value_label.setText("RGB: (N/A)")

        if self.auto_copy:
            self.copy_to_clipboard()

    def toggle_mode(self) -> None:
        """Toggle between coordinates and RGB mode"""
        if self.mode == 'coordinates':
            self.mode = 'rgb'
            self.status_label.setText("Mode: RGB")
        else:
            self.mode = 'coordinates'
            self.status_label.setText("Mode: Coordinates")
            self.value_label.setText(f"RGB: ({self.last_position[0]}, {self.last_position[1]})")

    def copy_to_clipboard(self) -> None:
        """Copy the current data to clipboard (coordinates or RGB)"""
        if self.mode == 'coordinates':
            data = f"({self.last_position[0]}, {self.last_position[1]})"
            pyperclip.copy(data)
        else:  # RGB mode
            data = f"({self.last_rgb[0]}, {self.last_rgb[1]}, {self.last_rgb[2]})"
            pyperclip.copy(data)

    def closeEvent(self, event) -> None: # type: ignore
        """Clean up resources when window is closed"""
        # Unregister all keyboard hotkeys
        keyboard.unhook_all()
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = PixelPicker()
    window.show()
    sys.exit(app.exec_())
