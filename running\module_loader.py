import os
import importlib.util
import sys
from typing import Dict, Any
from pathlib import Path

class ModuleLoader:
    def __init__(self):
        self.modules: Dict[str, Any] = {}
        self.modules_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "modules")
        
        # Create modules directory if it doesn't exist
        if not os.path.exists(self.modules_dir):
            os.makedirs(self.modules_dir)
            
        # Create __init__.py in modules directory
        self._create_init_file(self.modules_dir)

    def _create_init_file(self, directory):
        """Create __init__.py in the specified directory if it doesn't exist"""
        init_file = os.path.join(directory, "__init__.py")
        if not os.path.exists(init_file):
            with open(init_file, "w") as f:
                f.write("")

    def _load_module_from_path(self, module_path: str, full_path: str) -> Dict[str, Any]:
        """Load a single module file and return its functions"""
        try:
            spec = importlib.util.spec_from_file_location(module_path, full_path)
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            
            # Check if module has required attributes
            if not hasattr(module, 'MODULE_NAME') or not hasattr(module, 'register_functions'):
                print(f"Warning: Module {module_path} missing required attributes")
                return {}
                
            return module.register_functions()
            
        except Exception as e:
            print(f"Error loading module {module_path}: {str(e)}")
            return {}

    def load_module(self, import_path: str) -> bool:
        """
        Load a specific module or package by import path
        Example paths: 
        - 'random.numbers'
        - 'random.strings.words'
        - 'random'
        """
        parts = import_path.split('.')
        current_path = self.modules_dir
        current_module_path = []

        for i, part in enumerate(parts):
            current_path = os.path.join(current_path, part)
            current_module_path.append(part)
            
            # Check if this is a directory
            if os.path.isdir(current_path):
                self._create_init_file(current_path)
                
                # If this is the last part, load all .py files in the directory
                if i == len(parts) - 1:
                    return self._load_directory(current_path, '.'.join(current_module_path))
                continue
                
            # Check for .py file
            py_file = f"{current_path}.py"
            if os.path.isfile(py_file):
                if i == len(parts) - 1:
                    functions = self._load_module_from_path('.'.join(current_module_path), py_file)
                    if functions:
                        self.modules[import_path] = functions
                        return True
                    return False
                    
        return False

    def _load_directory(self, directory: str, base_path: str) -> bool:
        """Load all .py files in a directory"""
        success = False
        for item in os.listdir(directory):
            if item == '__init__.py':
                continue
                
            full_path = os.path.join(directory, item)
            if item.endswith('.py'):
                module_name = item[:-3]
                import_path = f"{base_path}.{module_name}"
                functions = self._load_module_from_path(import_path, full_path)
                if functions:
                    self.modules[import_path] = functions
                    success = True
            elif os.path.isdir(full_path):
                if self._load_directory(full_path, f"{base_path}.{item}"):
                    success = True
                    
        return success

    def get_function(self, module_path: str, func_name: str):
        """Get a specific function from a module"""
        # Try direct access first
        if module_path in self.modules and func_name in self.modules[module_path]:
            return self.modules[module_path][func_name]
            
        # Try nested access
        for registered_path, functions in self.modules.items():
            if registered_path.startswith(module_path + '.'):
                if func_name in functions:
                    return functions[func_name]
        return None
