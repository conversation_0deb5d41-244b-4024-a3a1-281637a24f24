import json
import os

current_dir = os.path.abspath(os.path.dirname(__file__))
SETTINGS_FILE = os.path.join(current_dir, "settings.json")

class Settings:
    def __init__(self) -> None:
        self.loadSettings()

    def update(self, new_settings: dict) -> None:
        self.settings.update(new_settings)
        self.saveSettings()

    def set(self, key: str, value: str) -> None:
        self.settings[key] = value
        self.saveSettings()

    def saveSettings(self) -> None:
        with open(SETTINGS_FILE, "w") as f:
            json.dump(self.settings, f, indent=4)

    def loadSettings(self) -> dict:
        if os.path.exists(SETTINGS_FILE):
            try:
                with open(SETTINGS_FILE, "r") as f:
                    self.settings = json.load(f)
                # Ensure all keys are valid
                self.validateSettings()
            except (json.JSONDecodeError, KeyError):
                self.settings = self.getDefaultSettings()
                self.saveSettings()
        else:
            self.settings = self.getDefaultSettings()
            self.saveSettings()
        return self.settings

    def getDefaultSettings(self) -> dict:
        return {
            "theme": "Dark",
            "macro_speed": 5,
            "start_key": "page up",
            "stop_key": "page down",
            "font_size": 14,
            "$prefix": False
        }

    def validateSettings(self) -> None:
        default_settings = self.getDefaultSettings()
        for key, default_value in default_settings.items():
            if key not in self.settings:
                self.settings[key] = default_value
        # Check for invalid theme
        if self.settings["theme"] not in ["Dark", "Light", "DarkxRed", "DarkxGreen", "DarkxBlue"]:
            self.settings["theme"] = "Dark"  # Default to Dark theme
