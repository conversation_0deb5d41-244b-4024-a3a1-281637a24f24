import pyautogui
import time
from datetime import datetime
from typing import Optional

MODULE_NAME = "image.find"

def register_functions() -> dict:
    return {
        'find': find,
        'wait_for': wait_for,
        'find_all': find_all
    }

def find(image_path: str, confidence: float = 0.9) -> Optional[pyautogui.Point]:
    """Find image on screen, return coordinates or None"""
    try:
        location = pyautogui.locateOnScreen(image_path, confidence=float(confidence))
        return pyautogui.center(location) if location else None # type: ignore
    except Exception:
        return None

def wait_for(image_path: str, timeout: float = 10, confidence: float = 0.9) -> Optional[pyautogui.Point]:
    """Wait for image to appear on screen"""
    start_time = datetime.now()
    timeout = float(timeout)
    confidence = float(confidence)
    
    while (datetime.now() - start_time).total_seconds() < timeout:
        try:
            location = pyautogui.locateOnScreen(image_path, confidence=confidence)
            if location:
                return pyautogui.center(location) # type: ignore
        except Exception:
            pass
        time.sleep(0.5)
    return None

def find_all(image_path: str, confidence: float = 0.9, limit: int = None) -> list: # type: ignore
    """Find all occurrences of an image on screen"""
    try:
        confidence = float(confidence)
        locations = list(pyautogui.locateAllOnScreen(image_path, confidence=confidence))
        if limit and len(locations) > int(limit):
            locations = locations[:int(limit)]
        return [pyautogui.center(location) for location in locations]
    except Exception:
        return []

