"""
Financial calculations module.
"""
import math

MODULE_NAME = "math.finance"

def register_functions():
    return {
        'simple_interest': simple_interest,
        'compound_interest': compound_interest,
        'loan_payment': loan_payment,
        'loan_principal': loan_principal,
        'loan_term': loan_term,
        'loan_rate': loan_rate,
        'future_value': future_value,
        'present_value': present_value,
        'roi': return_on_investment,
        'discount': discount_amount,
        'markup': markup_amount,
        'tax': tax_amount,
        'tip': tip_amount,
        'depreciation': straight_line_depreciation
    }

def simple_interest(principal, rate, time):
    """Calculate simple interest
    
    Args:
        principal: Principal amount
        rate: Annual interest rate (as a decimal, e.g., 0.05 for 5%)
        time: Time period in years
    
    Returns:
        The simple interest amount
    """
    return float(principal) * float(rate) * float(time)

def compound_interest(principal, rate, time, compounds_per_year=1):
    """Calculate compound interest
    
    Args:
        principal: Principal amount
        rate: Annual interest rate (as a decimal, e.g., 0.05 for 5%)
        time: Time period in years
        compounds_per_year: Number of times interest is compounded per year
    
    Returns:
        The final amount after compound interest
    """
    principal = float(principal)
    rate = float(rate)
    time = float(time)
    compounds_per_year = int(compounds_per_year)
    
    return principal * (1 + rate / compounds_per_year) ** (compounds_per_year * time)

def loan_payment(principal, rate, time):
    """Calculate monthly loan payment
    
    Args:
        principal: Loan amount
        rate: Annual interest rate (as a decimal, e.g., 0.05 for 5%)
        time: Loan term in years
    
    Returns:
        The monthly payment amount
    """
    principal = float(principal)
    monthly_rate = float(rate) / 12
    months = float(time) * 12
    
    if monthly_rate == 0:
        return principal / months
    
    return principal * (monthly_rate * (1 + monthly_rate) ** months) / ((1 + monthly_rate) ** months - 1)

def loan_principal(payment, rate, time):
    """Calculate loan principal based on payment
    
    Args:
        payment: Monthly payment amount
        rate: Annual interest rate (as a decimal, e.g., 0.05 for 5%)
        time: Loan term in years
    
    Returns:
        The loan principal amount
    """
    payment = float(payment)
    monthly_rate = float(rate) / 12
    months = float(time) * 12
    
    if monthly_rate == 0:
        return payment * months
    
    return payment * ((1 + monthly_rate) ** months - 1) / (monthly_rate * (1 + monthly_rate) ** months)

def loan_term(principal, payment, rate):
    """Calculate loan term based on principal and payment
    
    Args:
        principal: Loan amount
        payment: Monthly payment amount
        rate: Annual interest rate (as a decimal, e.g., 0.05 for 5%)
    
    Returns:
        The loan term in years
    """
    principal = float(principal)
    payment = float(payment)
    monthly_rate = float(rate) / 12
    
    if monthly_rate == 0:
        return principal / payment / 12
    
    if payment <= principal * monthly_rate:
        return "Error: Payment too small to repay loan"
    
    return math.log(payment / (payment - principal * monthly_rate)) / (12 * math.log(1 + monthly_rate))

def loan_rate(principal, payment, time):
    """Estimate annual interest rate based on principal, payment, and time
    
    Args:
        principal: Loan amount
        payment: Monthly payment amount
        time: Loan term in years
    
    Returns:
        The estimated annual interest rate as a decimal
    """
    principal = float(principal)
    payment = float(payment)
    months = float(time) * 12
    
    # This is an approximation using a simple formula
    # For exact calculation, an iterative approach would be needed
    return (payment * months - principal) / (principal * months) * 12

def future_value(present_value, rate, time, compounds_per_year=1):
    """Calculate future value of an investment
    
    Args:
        present_value: Current value
        rate: Annual interest rate (as a decimal, e.g., 0.05 for 5%)
        time: Time period in years
        compounds_per_year: Number of times interest is compounded per year
    
    Returns:
        The future value
    """
    return compound_interest(present_value, rate, time, compounds_per_year)

def present_value(future_value, rate, time, compounds_per_year=1):
    """Calculate present value needed to reach a future value
    
    Args:
        future_value: Target future value
        rate: Annual interest rate (as a decimal, e.g., 0.05 for 5%)
        time: Time period in years
        compounds_per_year: Number of times interest is compounded per year
    
    Returns:
        The present value needed
    """
    future_value = float(future_value)
    rate = float(rate)
    time = float(time)
    compounds_per_year = int(compounds_per_year)
    
    return future_value / ((1 + rate / compounds_per_year) ** (compounds_per_year * time))

def return_on_investment(initial_investment, final_value):
    """Calculate Return on Investment (ROI)
    
    Args:
        initial_investment: Initial investment amount
        final_value: Final value of the investment
    
    Returns:
        The ROI as a decimal
    """
    initial_investment = float(initial_investment)
    final_value = float(final_value)
    
    if initial_investment == 0:
        return "Error: Initial investment cannot be zero"
    
    return (final_value - initial_investment) / initial_investment

def discount_amount(original_price, discount_rate):
    """Calculate discount amount
    
    Args:
        original_price: Original price
        discount_rate: Discount rate as a decimal (e.g., 0.2 for 20%)
    
    Returns:
        The discount amount
    """
    return round(float(original_price) * float(discount_rate), 2)

def markup_amount(cost, markup_rate):
    """Calculate markup amount
    
    Args:
        cost: Cost price
        markup_rate: Markup rate as a decimal (e.g., 0.5 for 50%)
    
    Returns:
        The markup amount
;    """
    return round(float(cost) * float(markup_rate), 2)

def tax_amount(price, tax_rate):
    """Calculate tax amount
    
    Args:
        price: Price before tax
        tax_rate: Tax rate as a decimal (e.g., 0.07 for 7%)
    
    Returns:
        The tax amount
    """
    return round(float(price) * float(tax_rate), 2)

def tip_amount(bill, tip_rate):
    """Calculate tip amount
    
    Args:
        bill: Bill amount
        tip_rate: Tip rate as a decimal (e.g., 0.15 for 15%)
    
    Returns:
        The tip amount
    """
    return round(float(bill) * float(tip_rate), 2)

def straight_line_depreciation(initial_value, salvage_value, useful_life):
    """Calculate annual straight-line depreciation
    
    Args:
        initial_value: Initial value of the asset
        salvage_value: Salvage value at the end of useful life
        useful_life: Useful life in years
    
    Returns:
        The annual depreciation amount
    """
    initial_value = float(initial_value)
    salvage_value = float(salvage_value)
    useful_life = float(useful_life)
    
    if useful_life <= 0:
        return "Error: Useful life must be positive"
    
    return (initial_value - salvage_value) / useful_life
