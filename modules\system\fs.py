import os
import shutil
from glob import glob
import platform

MODULE_NAME = "system.fs"

def register_functions():
    return {
        'get_path': get_path,
        'exists': path_exists,
        'list_dir': list_directory,
        'create_dir': create_directory,
        'delete': delete_path,
        'move': move_path,
        'copy': copy_path
    }

def get_path(path_type="home"):
    """Get system paths
    Types: home, temp, current, documents"""
    paths = {
        "home": os.path.expanduser("~"),
        "temp": os.path.expandvars("%TEMP%") if platform.system() == "Windows" else "/tmp",
        "current": os.getcwd(),
        "documents": os.path.join(os.path.expanduser("~"), "Documents")
    }
    return paths.get(path_type, "")

def path_exists(path):
    """Check if path exists"""
    return os.path.exists(path)

def list_directory(path=".", pattern="*"):
    """List directory contents"""
    return glob(os.path.join(path, pattern))

def create_directory(path):
    """Create directory and parents if needed"""
    os.makedirs(path, exist_ok=True)
    return path

def delete_path(path):
    """Delete file or directory"""
    if os.path.isfile(path):
        os.remove(path)
    elif os.path.isdir(path):
        shutil.rmtree(path)
    return True

def move_path(source, destination):
    """Move file or directory"""
    shutil.move(source, destination)
    return destination

def copy_path(source, destination):
    """Copy file or directory"""
    if os.path.isfile(source):
        shutil.copy2(source, destination)
    else:
        shutil.copytree(source, destination)
    return destination
