# Test file for nested function calls

# Import modules
from math import basic

# Define variables
x = 5
y = 10

# Test nested module function calls
print Testing nested module function calls:
result1 = $basic.add($basic.multiply($x, 2), $y)
print $x * 2 + $y = $result1

# Define a function
func double(n):
    return $basic.multiply($n, 2)

# Define another function that uses the first function
func addDoubled(a, b):
    doubled_a = $double($a)
    doubled_b = $double($b)
    return $basic.add($doubled_a, $doubled_b)

# Test nested user function calls
print Testing nested user function calls:
result2 = $addDoubled($x, $y)
print double($x) + double($y) = $result2

# Test deeply nested calls
print Testing deeply nested calls:
result3 = $basic.add($double($basic.add($x, $y)), $basic.multiply(3, $double(2)))
print double($x + $y) + 3 * double(2) = $result3

# Test mixed module and user function calls
print Testing mixed function calls:
result4 = $double($basic.add($x, $y))
print double($x + $y) = $result4
