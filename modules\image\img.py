import pyautogui
from PIL import Image
from datetime import datetime
from typing import Optional

MODULE_NAME = "image.img"

def register_functions() -> dict:
    return {
        'screenshot': screenshot,
        'pixel': pixel,
        'rgb': pixel,
        'region': region
    }

def capture_screen() -> Image: # type: ignore
    """Capture full screen"""
    return pyautogui.screenshot() # type: ignore

def capture_region(x: int, y: int, width: int, height: int) -> Image: # type: ignore
    """Capture specific region"""
    return pyautogui.screenshot(region=(x, y, width, height)) # type: ignore

def screenshot(region: Optional[str] = None, filename: Optional[str] = None) -> str:
    """Take a screenshot and save to file"""
    try:
        if filename is None:
            filename = f"screenshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

        if region:
            x, y, w, h = map(int, region.split(','))
            img = capture_region(x, y, w, h)
        else:
            img = capture_screen()

        img.save(filename)
        return filename
    except Exception:
        return ""

def pixel(x: int, y: int) -> tuple:
    """Get RGB color at coordinates"""
    return pyautogui.pixel(int(x), int(y))

def region(x: int, y: int, width: int, height: int, filename: Optional[str] = None) -> str:
    """Capture a specific region to a file"""
    try:
        if filename is None:
            filename = f"region_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"

        img = capture_region(int(x), int(y), int(width), int(height))
        img.save(filename)
        return filename
    except Exception:
        return ""
