"""
Advanced mathematical operations module.
"""
import math

MODULE_NAME = "math.advanced"

def register_functions():
    return {
        'sin': sin,
        'cos': cos,
        'tan': tan,
        'asin': asin,
        'acos': acos,
        'atan': atan,
        'degrees': degrees,
        'radians': radians,
        'log': logarithm,
        'log10': log10,
        'exp': exp,
        'is_prime': is_prime,
        'next_prime': next_prime,
        'fibon<PERSON><PERSON>': <PERSON><PERSON><PERSON><PERSON>
    }

def sin(angle, is_degrees=True):
    """Calculate the sine of an angle"""
    if is_degrees and is_degrees != "False" and is_degrees != "0":
        angle = math.radians(float(angle))
    return math.sin(float(angle))

def cos(angle, is_degrees=True):
    """Calculate the cosine of an angle"""
    if is_degrees and is_degrees != "False" and is_degrees != "0":
        angle = math.radians(float(angle))
    return math.cos(float(angle))

def tan(angle, is_degrees=True):
    """Calculate the tangent of an angle"""
    if is_degrees and is_degrees != "False" and is_degrees != "0":
        angle = math.radians(float(angle))
    return math.tan(float(angle))

def asin(value):
    """Calculate the arcsine (inverse sine) in radians"""
    value = float(value)
    if value < -1 or value > 1:
        return "Error: Value must be between -1 and 1"
    return math.asin(value)

def acos(value):
    """Calculate the arccosine (inverse cosine) in radians"""
    value = float(value)
    if value < -1 or value > 1:
        return "Error: Value must be between -1 and 1"
    return math.acos(value)

def atan(value):
    """Calculate the arctangent (inverse tangent) in radians"""
    return math.atan(float(value))

def degrees(radians):
    """Convert radians to degrees"""
    return math.degrees(float(radians))

def radians(degrees):
    """Convert degrees to radians"""
    return math.radians(float(degrees))

def logarithm(value, base=None):
    """Calculate the logarithm of a value with specified base (natural log if base is None)"""
    value = float(value)
    if value <= 0:
        return "Error: Value must be positive"
    
    if base is None:
        return math.log(value)
    else:
        base = float(base)
        if base <= 0 or base == 1:
            return "Error: Base must be positive and not equal to 1"
        return math.log(value, base)

def log10(value):
    """Calculate the base-10 logarithm of a value"""
    value = float(value)
    if value <= 0:
        return "Error: Value must be positive"
    return math.log10(value)

def exp(value):
    """Calculate e raised to the power of value"""
    return math.exp(float(value))

def is_prime(number):
    """Check if a number is prime"""
    number = int(number)
    if number <= 1:
        return False
    if number <= 3:
        return True
    if number % 2 == 0 or number % 3 == 0:
        return False
    i = 5
    while i * i <= number:
        if number % i == 0 or number % (i + 2) == 0:
            return False
        i += 6
    return True

def next_prime(number):
    """Find the next prime number after the given number"""
    number = int(number)
    if number < 2:
        return 2
    
    # Start with the next number
    number += 1
    # Make sure it's odd
    if number % 2 == 0:
        number += 1
    
    # Keep checking until we find a prime
    while not is_prime(number):
        number += 2
    
    return number

def fibonacci(n):
    """Calculate the nth Fibonacci number"""
    n = int(n)
    if n < 0:
        return "Error: Input must be a non-negative integer"
    if n <= 1:
        return n
    
    a, b = 0, 1
    for _ in range(2, n + 1):
        a, b = b, a + b
    return b
