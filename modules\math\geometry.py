"""
Geometric calculations module.
"""
import math

MODULE_NAME = "math.geometry"

def register_functions():
    return {
        # 2D shapes
        'circle_area': circle_area,
        'circle_circumference': circle_circumference,
        'rectangle_area': rectangle_area,
        'rectangle_perimeter': rectangle_perimeter,
        'square_area': square_area,
        'square_perimeter': square_perimeter,
        'triangle_area': triangle_area,
        'triangle_perimeter': triangle_perimeter,
        'triangle_area_heron': triangle_area_heron,
        'ellipse_area': ellipse_area,
        'regular_polygon_area': regular_polygon_area,
        
        # 3D shapes
        'sphere_volume': sphere_volume,
        'sphere_surface_area': sphere_surface_area,
        'cube_volume': cube_volume,
        'cube_surface_area': cube_surface_area,
        'cylinder_volume': cylinder_volume,
        'cylinder_surface_area': cylinder_surface_area,
        'cone_volume': cone_volume,
        'cone_surface_area': cone_surface_area,
        'pyramid_volume': pyramid_volume,
        
        # Distance and angles
        'distance_2d': distance_2d,
        'distance_3d': distance_3d,
        'midpoint_2d': midpoint_2d,
        'midpoint_3d': midpoint_3d,
        'angle_between_points': angle_between_points,
        'angle_between_vectors': angle_between_vectors,
        'slope': slope,
        'point_line_distance': point_line_distance,
        'distance': distance,
    }

# 2D Shapes

def circle_area(radius):
    """Calculate the area of a circle"""
    radius = float(radius)
    return math.pi * radius ** 2

def circle_circumference(radius):
    """Calculate the circumference of a circle"""
    radius = float(radius)
    return 2 * math.pi * radius

def rectangle_area(length, width):
    """Calculate the area of a rectangle"""
    return float(length) * float(width)

def rectangle_perimeter(length, width):
    """Calculate the perimeter of a rectangle"""
    return 2 * (float(length) + float(width))

def square_area(side):
    """Calculate the area of a square"""
    side = float(side)
    return side ** 2

def square_perimeter(side):
    """Calculate the perimeter of a square"""
    return 4 * float(side)

def triangle_area(base, height):
    """Calculate the area of a triangle using base and height"""
    return 0.5 * float(base) * float(height)

def triangle_perimeter(side1, side2, side3):
    """Calculate the perimeter of a triangle"""
    return float(side1) + float(side2) + float(side3)

def triangle_area_heron(side1, side2, side3):
    """Calculate the area of a triangle using Heron's formula"""
    side1, side2, side3 = float(side1), float(side2), float(side3)
    
    # Check if the sides can form a triangle
    if side1 + side2 <= side3 or side1 + side3 <= side2 or side2 + side3 <= side1:
        return "Error: These sides cannot form a triangle"
    
    # Semi-perimeter
    s = (side1 + side2 + side3) / 2
    
    # Heron's formula
    return math.sqrt(s * (s - side1) * (s - side2) * (s - side3))

def ellipse_area(semi_major_axis, semi_minor_axis):
    """Calculate the area of an ellipse"""
    return math.pi * float(semi_major_axis) * float(semi_minor_axis)

def regular_polygon_area(sides, side_length):
    """Calculate the area of a regular polygon"""
    sides = int(sides)
    side_length = float(side_length)
    
    if sides < 3:
        return "Error: A polygon must have at least 3 sides"
    
    # Calculate the area using the formula: (n * s^2) / (4 * tan(π/n))
    return (sides * side_length ** 2) / (4 * math.tan(math.pi / sides))

# 3D Shapes

def sphere_volume(radius):
    """Calculate the volume of a sphere"""
    radius = float(radius)
    return (4/3) * math.pi * radius ** 3

def sphere_surface_area(radius):
    """Calculate the surface area of a sphere"""
    radius = float(radius)
    return 4 * math.pi * radius ** 2

def cube_volume(side):
    """Calculate the volume of a cube"""
    side = float(side)
    return side ** 3

def cube_surface_area(side):
    """Calculate the surface area of a cube"""
    side = float(side)
    return 6 * side ** 2

def cylinder_volume(radius, height):
    """Calculate the volume of a cylinder"""
    radius = float(radius)
    height = float(height)
    return math.pi * radius ** 2 * height

def cylinder_surface_area(radius, height):
    """Calculate the surface area of a cylinder"""
    radius = float(radius)
    height = float(height)
    return 2 * math.pi * radius * (radius + height)

def cone_volume(radius, height):
    """Calculate the volume of a cone"""
    radius = float(radius)
    height = float(height)
    return (1/3) * math.pi * radius ** 2 * height

def cone_surface_area(radius, height):
    """Calculate the surface area of a cone"""
    radius = float(radius)
    height = float(height)
    slant_height = math.sqrt(radius ** 2 + height ** 2)
    return math.pi * radius * (radius + slant_height)

def pyramid_volume(base_area, height):
    """Calculate the volume of a pyramid"""
    return (1/3) * float(base_area) * float(height)

# Distance and Angles

def distance_2d(x1, y1, x2, y2):
    """Calculate the distance between two points in 2D space"""
    x1, y1, x2, y2 = float(x1), float(y1), float(x2), float(y2)
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)

def distance_3d(x1, y1, z1, x2, y2, z2):
    """Calculate the distance between two points in 3D space"""
    x1, y1, z1 = float(x1), float(y1), float(z1)
    x2, y2, z2 = float(x2), float(y2), float(z2)
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2 + (z2 - z1) ** 2)

def midpoint_2d(x1, y1, x2, y2):
    """Calculate the midpoint between two points in 2D space"""
    x1, y1, x2, y2 = float(x1), float(y1), float(x2), float(y2)
    return ((x1 + x2) / 2, (y1 + y2) / 2)

def midpoint_3d(x1, y1, z1, x2, y2, z2):
    """Calculate the midpoint between two points in 3D space"""
    x1, y1, z1 = float(x1), float(y1), float(z1)
    x2, y2, z2 = float(x2), float(y2), float(z2)
    return ((x1 + x2) / 2, (y1 + y2) / 2, (z1 + z2) / 2)

def angle_between_points(x1, y1, x2, y2):
    """Calculate the angle (in radians) between two points relative to the x-axis"""
    x1, y1, x2, y2 = float(x1), float(y1), float(x2), float(y2)
    return math.atan2(y2 - y1, x2 - x1)

def angle_between_vectors(x1, y1, x2, y2):
    """Calculate the angle (in radians) between two vectors"""
    x1, y1, x2, y2 = float(x1), float(y1), float(x2), float(y2)
    
    # Calculate the dot product
    dot_product = x1 * x2 + y1 * y2
    
    # Calculate the magnitudes
    mag1 = math.sqrt(x1 ** 2 + y1 ** 2)
    mag2 = math.sqrt(x2 ** 2 + y2 ** 2)
    
    if mag1 == 0 or mag2 == 0:
        return "Error: Vector magnitude cannot be zero"
    
    # Calculate the angle using the dot product formula
    cos_angle = dot_product / (mag1 * mag2)
    
    # Handle floating-point errors
    if cos_angle > 1:
        cos_angle = 1
    elif cos_angle < -1:
        cos_angle = -1
    
    return math.acos(cos_angle)

def slope(x1, y1, x2, y2):
    """Calculate the slope of a line between two points"""
    x1, y1, x2, y2 = float(x1), float(y1), float(x2), float(y2)
    
    if x2 - x1 == 0:
        return "Infinity (vertical line)"
    
    return (y2 - y1) / (x2 - x1)

def point_line_distance(px, py, x1, y1, x2, y2):
    """Calculate the distance from a point to a line defined by two points"""
    px, py = float(px), float(py)
    x1, y1, x2, y2 = float(x1), float(y1), float(x2), float(y2)
    
    # If the line is just a point
    if x1 == x2 and y1 == y2:
        return distance_2d(px, py, x1, y1)
    
    # Calculate the distance using the formula: |Ax + By + C| / sqrt(A^2 + B^2)
    # Where Ax + By + C = 0 is the line equation
    A = y2 - y1
    B = x1 - x2
    C = x2 * y1 - x1 * y2
    
    return abs(A * px + B * py + C) / math.sqrt(A ** 2 + B ** 2)

def distance(x1, y1, x2, y2):
    """Calculate the Euclidean distance between two points (x1,y1) and (x2,y2)

    This is a convenient shorthand for the distance formula:
    sqrt((x2-x1)² + (y2-y1)²)
    """
    result = math.sqrt((float(x2) - float(x1))**2 + (float(y2) - float(y1))**2)
    # Format the result to remove unnecessary decimal points
    return int(result) if float(result).is_integer() else result
