import platform
import sys
import psutil

MODULE_NAME = "system.info"

def register_functions():
    return {
        'get_info': system_info,
        'memory_usage': memory_usage,
        'cpu_usage': cpu_usage,
        'disk_space': disk_space
    }

def system_info():
    """Get system information"""
    return {
        "os": platform.system(),         # Just returns "Windows", "Linux", etc.
        "version": platform.version(),
        "machine": platform.machine(),
        "processor": platform.processor(),
        "python": sys.version
    }

def memory_usage():
    """Get memory usage information"""
    mem = psutil.virtual_memory()
    return {
        "total": mem.total,
        "available": mem.available,
        "percent": mem.percent,
        "used": mem.used
    }

def cpu_usage():
    """Get CPU usage percentage"""
    return psutil.cpu_percent(interval=1)

def disk_space(path="/"):
    """Get disk space information"""
    disk = psutil.disk_usage(path)
    return {
        "total": disk.total,
        "used": disk.used,
        "free": disk.free,
        "percent": disk.percent
    }
