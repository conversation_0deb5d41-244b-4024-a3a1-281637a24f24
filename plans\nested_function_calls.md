# Implementation Plan: Nested Function Calls

## Overview

This document outlines the steps required to implement nested function calls in the macro language. Nested function calls allow for more concise and expressive code by combining multiple operations in a single line, such as `$basic.add($basic.multiply($x, 3), 1)`.

## Current Limitations

Currently, the interpreter can handle basic function calls but not nested ones. When a function call contains another function call as an argument, the inner function call is not evaluated before being passed to the outer function.

## Implementation Strategy

### 1. Modify the Variable Replacement System

#### Current Behavior
The current variable replacement system likely processes a line once, replacing variables with their values. It doesn't recursively process function calls within function arguments.

#### Required Changes
1. Implement a recursive variable replacement system that:
   - Identifies function calls within function arguments
   - Evaluates inner function calls before outer ones
   - Replaces the inner function call with its return value

### 2. Function Call Detection and Processing

#### Implementation Steps

1. **Modify the `replace_variables` method in the `Interpreter` class:**
   ```python
   def replace_variables(self, code: str):
       # First, check for nested function calls
       if '$' in code:
           # Process function calls from innermost to outermost
           code = self.process_nested_functions(code)
       
       # Then proceed with normal variable replacement
       # (existing code)
       
       return processed_code
   ```

2. **Create a new `process_nested_functions` method:**
   ```python
   def process_nested_functions(self, code: str):
       # Keep processing until no more nested functions are found
       max_iterations = 10  # Prevent infinite loops
       iteration = 0
       
       while '$' in code and iteration < max_iterations:
           iteration += 1
           
           # Find the innermost function call (one without nested calls)
           pattern = r'\$([a-zA-Z][a-zA-Z0-9_]*(?:\.[a-zA-Z][a-zA-Z0-9_]*)*)\(([^$()]*)\)'
           match = re.search(pattern, code)
           
           if not match:
               break
               
           full_match = match.group(0)  # The entire matched function call
           function_name = match.group(1)
           args_str = match.group(2)
           
           # Process the function call
           result = self.process_function_call(function_name, args_str)
           
           # Replace the function call with its result
           code = code.replace(full_match, str(result), 1)
       
       return code
   ```

3. **Create a `process_function_call` method:**
   ```python
   def process_function_call(self, function_name, args_str):
       # Handle module function calls (e.g., basic.add)
       if '.' in function_name:
           module_name, func_name = function_name.rsplit('.', 1)
           
           # Parse and process arguments
           args = []
           if args_str.strip():
               args = [arg.strip() for arg in args_str.split(',')]
               
           # Process each argument (replace variables)
           processed_args = []
           for arg in args:
               processed_arg = self.replace_variables(arg)
               processed_args.append(processed_arg)
           
           # Call the module function
           try:
               result = self.bultiin_functions.call_module_function(
                   module_name, func_name, *processed_args)
               return result
           except Exception as e:
               self.error("Function Call Error", str(e))
               return f"Error: {str(e)}"
       
       # Handle user-defined functions
       elif function_name in self.functions:
           # Similar logic for user-defined functions
           # ...
       
       else:
           self.error("Function Error", f"Function '{function_name}' not found")
           return f"Error: Function {function_name} not found"
   ```

### 3. Handling User-Defined Functions

For user-defined functions, we need to:
1. Parse the arguments
2. Create a temporary variable scope
3. Execute the function body
4. Return the result

```python
# Inside process_function_call method
elif function_name in self.functions:
    function_info = self.functions[function_name]
    expected_args = function_info["arg_count"]
    arg_names = function_info.get("args", [])
    
    # Parse arguments
    actual_args = []
    if args_str.strip():
        actual_args = [arg.strip() for arg in args_str.split(',')]
    
    # Validate argument count
    if len(actual_args) != expected_args:
        self.error("Function Call Error",
                  f"Function '{function_name}' expects {expected_args} arguments, but got {len(actual_args)}")
        return f"Error: Function {function_name} argument count mismatch"
    
    # Process arguments (replace variables)
    processed_args = []
    for arg in actual_args:
        processed_arg = self.replace_variables(arg)
        processed_args.append(processed_arg)
    
    # Create temporary variables for function arguments
    temp_vars = {}
    for arg_name, arg_value in zip(arg_names, processed_args):
        temp_vars[arg_name] = arg_value
    
    # Save current variables and set function arguments
    saved_vars = self.variables.copy()
    self.variables.update(temp_vars)
    
    # Execute function body
    return_value = None
    for line in function_info["body"].splitlines():
        if line.strip():
            if line.strip().startswith('return '):
                # Process return statement
                return_expr = line.strip()[7:].strip()
                return_value = self.replace_variables(return_expr)
                break
            else:
                # Execute the line
                self.parse_line(line)
    
    # Restore original variables
    self.variables = saved_vars
    
    return return_value
```

### 4. Handling Recursive Function Calls

We need to ensure our implementation can handle recursive function calls without infinite loops:

1. Add a recursion depth counter
2. Set a maximum recursion depth (e.g., 100)
3. Track the call stack for better error messages

```python
def process_function_call(self, function_name, args_str, depth=0):
    # Check recursion depth
    if depth > 100:
        self.error("Recursion Error", "Maximum recursion depth exceeded")
        return "Error: Maximum recursion depth exceeded"
    
    # Rest of the implementation
    # ...
    
    # When calling recursively, increment depth
    # For example, when processing nested function calls:
    result = self.process_function_call(inner_function, inner_args, depth + 1)
```

### 5. Error Handling

Robust error handling is crucial for nested function calls:

1. **Syntax Errors**: Detect and report malformed function calls
2. **Type Errors**: Handle type mismatches in function arguments
3. **Recursion Errors**: Prevent infinite recursion
4. **Missing Functions**: Handle calls to undefined functions
5. **Argument Count Errors**: Validate argument counts

### 6. Testing Strategy

1. **Basic Nested Calls**: Test simple nested function calls
   ```
   result = $basic.add($basic.multiply(2, 3), 4)
   ```

2. **Multiple Levels of Nesting**: Test deeply nested calls
   ```
   result = $basic.add($basic.multiply(2, $basic.add(3, 4)), 5)
   ```

3. **User-Defined Functions**: Test nested calls with user-defined functions
   ```
   func double(x):
       return $basic.multiply($x, 2)
   
   result = $basic.add($double(5), 3)
   ```

4. **Mixed Module and User Functions**: Test combinations
   ```
   func increment(x):
       return $basic.add($x, 1)
   
   result = $increment($basic.multiply(2, 3))
   ```

5. **Error Cases**: Test error handling
   ```
   # Missing function
   result = $basic.add($nonexistent(5), 3)
   
   # Wrong argument count
   result = $basic.add($basic.multiply(2), 3)
   ```

## Implementation Timeline

1. **Step 1**: Modify the variable replacement system to detect nested function calls
2. **Step 2**: Implement the processing of nested module function calls
3. **Step 3**: Add support for nested user-defined function calls
4. **Step 4**: Implement error handling and recursion protection
5. **Step 5**: Testing and debugging

## Conclusion

Implementing nested function calls will significantly enhance the expressiveness and power of the macro language. It will allow users to write more concise code and create more complex operations in a single line. The implementation requires careful handling of variable scopes, function evaluation order, and error conditions, but will result in a much more flexible and powerful macro language.
