"""
Basic mathematical operations module.
"""
import math

MODULE_NAME = "math.basic"

def register_functions():
    return {
        'add': add,
        'sum': add,
        'total': add,
        'subtract': subtract,
        'difference': subtract,

        'product': multiply,
        'multiply': multiply,
        'divide': divide,
        'quotient': divide,

        'power': power,
        'sqrt': sqrt,

        'round': round_number,
        'round_up': round_up,
        'round_down': round_down,

        'floor': floor,
        'ceil': ceil,

        'gcd': gcd,
        'lcm': lcm,

        'abs': absolute,
        'absolute': absolute,

        'mod': modulo,
        'remainder': modulo,

        'factorial': factorial,
        'average': average,
    }

def add(*args):
    """Add multiple numbers together"""
    result = 0
    for arg in args:
        # Skip error messages
        if isinstance(arg, str) and arg.startswith('Error:'):
            return arg

        # Skip variable references that weren't replaced
        if isinstance(arg, str) and arg.startswith('$'):
            # Just continue with the next argument
            continue

        try:
            result += float(arg)
        except (ValueError, TypeError):
            # Don't return an error, just skip this argument
            continue

    # Format the result to remove unnecessary decimal points
    return int(result) if float(result).is_integer() else result

def subtract(*args):
    """Subtract all subsequent numbers from the first number

    Example: subtract(10, 2, 3) = 10 - 2 - 3 = 5
    """
    if not args:
        return 0

    # Check first argument
    if isinstance(args[0], str) and args[0].startswith('Error:'):
        return args[0]

    # Skip variable references that weren't replaced
    if isinstance(args[0], str) and args[0].startswith('$'):
        # Use 0 as the default value
        result = 0
    else:
        try:
            result = float(args[0])
        except (ValueError, TypeError):
            # Use 0 as the default value
            result = 0

    for arg in args[1:]:
        # Skip error messages
        if isinstance(arg, str) and arg.startswith('Error:'):
            return arg

        # Skip variable references that weren't replaced
        if isinstance(arg, str) and arg.startswith('$'):
            # Just continue with the next argument
            continue

        try:
            result -= float(arg)
        except (ValueError, TypeError):
            # Don't return an error, just skip this argument
            continue

    # Format the result to remove unnecessary decimal points
    return int(result) if float(result).is_integer() else result

def multiply(*args):
    """Multiply multiple numbers together"""
    result = 1
    for arg in args:
        # Skip error messages
        if isinstance(arg, str) and arg.startswith('Error:'):
            return arg
        try:
            result *= float(arg)
        except (ValueError, TypeError):
            return f"Error: Cannot convert '{arg}' to a number"
    # Format the result to remove unnecessary decimal points
    return int(result) if float(result).is_integer() else result

def divide(a, b):
    """Divide a by b"""
    # Check for error messages
    if isinstance(a, str) and a.startswith('Error:'):
        return a
    if isinstance(b, str) and b.startswith('Error:'):
        return b

    # Convert to float with error handling
    try:
        a_float = float(a)
    except (ValueError, TypeError):
        return f"Error: Cannot convert '{a}' to a number"

    try:
        b_float = float(b)
    except (ValueError, TypeError):
        return f"Error: Cannot convert '{b}' to a number"

    if b_float == 0:
        return "Error: Division by zero"

    result = a_float / b_float
    # Format the result to remove unnecessary decimal points
    return int(result) if float(result).is_integer() else result

def power(base, exponent):
    """Raise base to the power of exponent"""
    result = math.pow(float(base), float(exponent))
    # Format the result to remove unnecessary decimal points
    return int(result) if float(result).is_integer() else result

def sqrt(number):
    """Calculate the square root of a number"""
    number = float(number)
    if number < 0:
        return "Error: Cannot calculate square root of negative number"
    result = math.sqrt(number)
    # Format the result to remove unnecessary decimal points
    return int(result) if float(result).is_integer() else result

def round_number(number, decimals=0):
    """Round a number to specified decimal places"""
    return round(float(number), int(decimals))

def floor(number):
    """Round down to the nearest integer"""
    return math.floor(float(number))

def ceil(number):
    """Round up to the nearest integer"""
    # Check for error messages
    if isinstance(number, str) and number.startswith('Error:'):
        return number

    # Skip variable references that weren't replaced
    if isinstance(number, str) and number.startswith('$'):
        # Return 0 as a default value
        return 0

    try:
        return math.ceil(float(number))
    except (ValueError, TypeError):
        # Return 0 as a default value instead of an error
        return 0

def absolute(number):
    """Get the absolute value of a number"""
    return abs(float(number))

def modulo(a, b):
    """Get the remainder of a divided by b"""
    b = float(b)
    if b == 0:
        return "Error: Division by zero"
    return float(a) % b

def factorial(number):
    """Calculate the factorial of a number"""
    try:
        return math.factorial(int(number))
    except ValueError:
        return "Error: Factorial requires a non-negative integer"

def gcd(a, b):
    """Calculate the greatest common divisor of two numbers"""
    return math.gcd(int(a), int(b))

def lcm(a, b):
    """Calculate the least common multiple of two numbers"""
    return abs(int(a) * int(b)) // math.gcd(int(a), int(b))

def average(*args):
    """Calculate the arithmetic mean of a list of numbers"""
    if not args:
        return 0

    # Calculate the sum
    sum_result = add(*args)

    # Check if the sum is an error message
    if isinstance(sum_result, str) and sum_result.startswith('Error:'):
        return sum_result

    # Calculate the average
    try:
        # Ensure sum_result is a number before division
        if isinstance(sum_result, (int, float)):
            return sum_result / len(args)
        else:
            return f"Error: Cannot calculate average - sum is not a number"
    except (TypeError, ValueError):
        return f"Error: Cannot calculate average"

def round_up(number, decimals=0):
    """Round up a number to specified decimal places"""
    # Check for error messages
    if isinstance(number, str) and number.startswith('Error:'):
        return number

    try:
        num = float(number)
        dec = int(decimals)
        return math.ceil(num * 10 ** dec) / 10 ** dec
    except (ValueError, TypeError):
        return f"Error: Cannot round up '{number}' to {decimals} decimal places"

def round_down(number, decimals=0):
    """Round down a number to specified decimal places"""
    # Check for error messages
    if isinstance(number, str) and number.startswith('Error:'):
        return number

    try:
        num = float(number)
        dec = int(decimals)
        return math.floor(num * 10 ** dec) / 10 ** dec
    except (ValueError, TypeError):
        return f"Error: Cannot round down '{number}' to {decimals} decimal places"
