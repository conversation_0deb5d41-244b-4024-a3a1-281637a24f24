# Implementation Plan: Real-Time Dynamic Syntax Highlighting for Macro Commands

## Overview
This document outlines the steps required to implement real-time syntax highlighting for macro commands in the code editor. The highlighting will make different elements of the macro language visually distinct, improving readability and reducing errors.

## 1. Create a Custom Syntax Highlighter Class

### Implementation Steps:
1. Create a new file `syntax_highlighter.py` in the `static` folder
2. Define a `MacroSyntaxHighlighter` class that inherits from `QSyntaxHighlighter`
3. Implement the `highlightBlock` method which is called automatically for each text block

```python
from PyQt5.QtCore import QRegExp, Qt
from PyQt5.QtGui import QSyntaxHighlighter, QTextCharFormat, QFont, QColor
from settings import settings_manager

class MacroSyntaxHighlighter(QSyntaxHighlighter):
    def __init__(self, document):
        super().__init__(document)
        self.settings = settings_manager.Settings()
        self.highlighting_rules = []
        self.setupHighlightingRules()
        
    def setupHighlightingRules(self):
        # Define formats and rules here
        pass
        
    def highlightBlock(self, text):
        # Apply rules to each text block
        for pattern, format in self.highlighting_rules:
            expression = QRegExp(pattern)
            index = expression.indexIn(text)
            while index >= 0:
                length = expression.matchedLength()
                self.setFormat(index, length, format)
                index = expression.indexIn(text, index + length)
```

## 2. Define Syntax Highlighting Rules

### Categories to Highlight:
1. **Commands**: wait, press, click, type, etc.
2. **Variables**: Any text starting with $
3. **Functions**: User-defined functions with the 'func' keyword
4. **Module Functions**: Functions called with module.function() syntax
5. **Comments**: Lines starting with #
6. **Strings**: Text in quotes
7. **Numbers**: Numeric literals
8. **Keywords**: import, from, as, etc.

### Implementation Steps:
1. Create format objects for each category with appropriate colors
2. Define regex patterns to match each category
3. Add pattern-format pairs to the highlighting rules list

```python
def setupHighlightingRules(self):
    # Get theme-appropriate colors
    theme = self.settings.settings["theme"].lower()
    self.setupThemeColors(theme)
    
    # Commands format
    command_format = QTextCharFormat()
    command_format.setForeground(QColor(self.command_color))
    command_format.setFontWeight(QFont.Bold)
    
    # Define command patterns
    commands = [
        "wait", "press", "hold", "release", "click", "rclick", "dclick",
        "mclick", "gotourl", "browser", "copy", "paste", "typef", "type",
        "move", "moveto", "print", "run", "console", "python"
    ]
    
    # Add each command as a rule
    for command in commands:
        pattern = f"\\b{command}\\b"
        self.highlighting_rules.append((pattern, command_format))
    
    # Variables format
    variable_format = QTextCharFormat()
    variable_format.setForeground(QColor(self.variable_color))
    
    # Variable pattern (starts with $)
    variable_pattern = "\\$[a-zA-Z_][a-zA-Z0-9_]*"
    self.highlighting_rules.append((variable_pattern, variable_format))
    
    # Continue with other categories...
```

## 3. Theme Integration

### Implementation Steps:
1. Define color schemes for both light and dark themes
2. Update colors when theme changes
3. Ensure good contrast for all syntax elements

```python
def setupThemeColors(self, theme):
    if theme.startswith("dark"):
        # Dark theme colors
        self.command_color = "#569CD6"  # Blue
        self.variable_color = "#9CDCFE"  # Light blue
        self.function_color = "#DCDCAA"  # Yellow
        self.module_color = "#4EC9B0"    # Teal
        self.comment_color = "#6A9955"   # Green
        self.string_color = "#CE9178"    # Orange
        self.number_color = "#B5CEA8"    # Light green
        self.keyword_color = "#C586C0"   # Purple
        
        # For accent themes
        if "red" in theme:
            self.accent_color = "#ff4f2e"
        elif "green" in theme:
            self.accent_color = "#3DDC84"
        elif "blue" in theme:
            self.accent_color = "#2e76ff"
        else:
            self.accent_color = "#3DDC84"
    else:
        # Light theme colors
        self.command_color = "#0000FF"   # Blue
        self.variable_color = "#001080"  # Dark blue
        self.function_color = "#795E26"  # Brown
        self.module_color = "#267f99"    # Teal
        self.comment_color = "#008000"   # Green
        self.string_color = "#A31515"    # Red
        self.number_color = "#098658"    # Dark green
        self.keyword_color = "#AF00DB"   # Purple
        self.accent_color = "#3DDC84"    # Default accent
```

## 4. Integration with the Editor

### Implementation Steps:
1. Import the `MacroSyntaxHighlighter` in the `editor.py` file
2. Initialize the highlighter in the `CodeEditor` class
3. Update the highlighter when the theme changes

```python
# In editor.py
from static.syntax_highlighter import MacroSyntaxHighlighter

class CodeEditor(QPlainTextEdit):
    def __init__(self):
        super().__init__()
        # Existing initialization code...
        
        # Initialize syntax highlighter
        self.highlighter = MacroSyntaxHighlighter(self.document())
        
    def applyTheme(self):
        # Existing theme application code...
        
        # Update syntax highlighter with new theme
        if hasattr(self, 'highlighter'):
            self.highlighter.setupThemeColors(settings.settings["theme"].lower())
            self.highlighter.rehighlight()  # Force rehighlight of the entire document
```

## 5. Performance Optimization

### Implementation Steps:
1. Optimize regex patterns for efficiency
2. Use QRegExp.PatternSyntax.RegExp2 for better performance
3. Consider batching updates for large files

```python
# Use more efficient regex engine
expression = QRegExp(pattern, Qt.CaseInsensitive, QRegExp.RegExp2)

# For large files, consider implementing a throttling mechanism
def throttledRehighlight(self):
    if not hasattr(self, '_rehighlight_timer'):
        self._rehighlight_timer = QTimer()
        self._rehighlight_timer.setSingleShot(True)
        self._rehighlight_timer.timeout.connect(self.rehighlight)
    
    self._rehighlight_timer.start(100)  # Wait 100ms before rehighlighting
```

## 6. Special Handling for Multi-line Constructs

### Implementation Steps:
1. Implement special handling for multi-line constructs like functions
2. Override the `highlightBlock` method to track state between blocks
3. Use the `setCurrentBlockState` and `previousBlockState` methods

```python
def highlightBlock(self, text):
    # Apply standard rules
    for pattern, format in self.highlighting_rules:
        # Apply pattern...
    
    # Handle multi-line constructs
    self.highlightMultilineFunction(text)

def highlightMultilineFunction(self, text):
    # States: 0 = normal, 1 = inside function definition
    
    # Get previous block state
    previous_state = self.previousBlockState()
    if previous_state == -1:
        previous_state = 0
    
    # Check if we're starting a function definition
    if "func " in text and ":" in text:
        # Format function definition
        func_format = QTextCharFormat()
        func_format.setForeground(QColor(self.function_color))
        func_format.setFontWeight(QFont.Bold)
        
        # Apply format to function keyword and name
        func_pattern = QRegExp("func\\s+([a-zA-Z_][a-zA-Z0-9_]*)")
        index = func_pattern.indexIn(text)
        if index >= 0:
            self.setFormat(index, func_pattern.matchedLength(), func_format)
        
        # Set state to inside function
        self.setCurrentBlockState(1)
    elif previous_state == 1 and text.strip().startswith("return "):
        # Format return statement
        return_format = QTextCharFormat()
        return_format.setForeground(QColor(self.keyword_color))
        return_format.setFontWeight(QFont.Bold)
        
        return_pattern = QRegExp("return\\b")
        index = return_pattern.indexIn(text)
        if index >= 0:
            self.setFormat(index, return_pattern.matchedLength(), return_format)
        
        # Continue function state
        self.setCurrentBlockState(1)
    elif previous_state == 1 and not text.strip():
        # Empty line inside function
        self.setCurrentBlockState(1)
    elif previous_state == 1 and not text.startswith("    "):
        # No longer indented - end of function
        self.setCurrentBlockState(0)
    elif previous_state == 1:
        # Still inside function
        self.setCurrentBlockState(1)
    else:
        # Normal state
        self.setCurrentBlockState(0)
```

## 7. Testing and Debugging

### Testing Steps:
1. Create test files with various macro commands and syntax
2. Test with both light and dark themes
3. Test with large files to ensure performance
4. Test with complex nested constructs

### Debugging Tips:
1. **Visual Debugging**: Add temporary highlighting with distinct colors to see what's being matched
   ```python
   # Temporary debug format with bright color
   debug_format = QTextCharFormat()
   debug_format.setBackground(QColor("magenta"))
   self.setFormat(index, length, debug_format)
   ```

2. **Print Debugging**: Add print statements to track what's happening
   ```python
   print(f"Highlighting block: '{text}', state: {self.previousBlockState()}")
   print(f"Found match at index {index}, length {length}")
   ```

3. **Rule Isolation**: Test one rule at a time to identify issues
   ```python
   # Comment out all rules except the one you're testing
   self.highlighting_rules = []
   self.highlighting_rules.append((variable_pattern, variable_format))
   # self.highlighting_rules.append((command_pattern, command_format))
   ```

4. **Regex Testing**: Use online regex testers to verify patterns
   - Test patterns at https://regex101.com/ with the PCRE flavor
   - Ensure patterns match what you expect

5. **Performance Profiling**:
   - Use Python's `cProfile` to identify bottlenecks
   - Monitor CPU usage during editing of large files
   - Consider implementing throttling for large documents

## 8. Future Enhancements

1. **User Customization**: Allow users to customize syntax colors
2. **Syntax Error Highlighting**: Highlight potential syntax errors
3. **Code Folding**: Add ability to collapse function definitions
4. **Auto-completion**: Suggest commands and variables as user types
5. **Tooltips**: Show documentation for commands on hover

## Implementation Timeline

1. **Step 1**: Create basic syntax highlighter structure and command highlighting
2. **Step 2**: Add variable, function, and other basic syntax elements
3. **Step 3**: Implement multi-line construct handling
4. **Step 4**: Theme integration and performance optimization
5. **Step 5**: Testing, debugging, and refinement

## Conclusion

This implementation will significantly improve the usability of the macro editor by providing visual cues for different syntax elements. The real-time highlighting will help users identify errors more quickly and understand the structure of their macro scripts at a glance.

Remember to maintain good performance even with larger files, and ensure the highlighting is visually consistent with the rest of the application's theme system.
