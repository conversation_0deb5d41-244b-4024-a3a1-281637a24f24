import psutil
import subprocess

MODULE_NAME = "system.process"

def register_functions():
    return {
        'execute': execute_command,
        'list': list_processes,
        'kill': kill_process
    }

def execute_command(command, shell=True):
    """Execute system command and return output"""
    try:
        result = subprocess.run(command, shell=shell, capture_output=True, text=True)
        return result.stdout if result.stdout else result.stderr
    except Exception as e:
        return str(e)

def list_processes():
    """List running processes"""
    processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
        processes.append(proc.info)
    return processes

def kill_process(pid):
    """Kill process by PID"""
    try:
        psutil.Process(pid).terminate()
        return True
    except:
        return False