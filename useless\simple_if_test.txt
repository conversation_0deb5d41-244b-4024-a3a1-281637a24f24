# Simple test for if statements

from math import basic

# Define variables
x = 10
y = 5

# Test if statements with simple values
print Testing if statements with simple values:

# Test equal comparison
if 10 == 10:
    print 10 equals 10 - This should display

# Test greater than comparison
if 10 > 5:
    print 10 is greater than 5 - This should display

# Test less than comparison
if 5 < 10:
    print 5 is less than 10 - This should display

# Test with variables
print Testing if statements with variables:

# Test equal comparison
if $x == 10:
    print x equals 10 - This should display

# Test greater than comparison
if $x > $y:
    print x is greater than y - This should display

# Test less than comparison
if $y < $x:
    print y is less than x - This should display

# Test with simple function calls
print Testing if statements with simple function calls:

# Test equal comparison
result1 = $basic.add(5, 5)
if $result1 == 10:
    print result1 equals 10 - This should display

# Test greater than comparison directly
if $basic.multiply(2, 3) > 5:
    print basic.multiply(2, 3) is greater than 5 - This should display

# Test less than comparison directly
if $basic.subtract(10, 5) < 10:
    print basic.subtract(10, 5) is less than 10 - This should display
