import pyautogui
from .find import find, wait_for

MODULE_NAME = "image.act"

def register_functions() -> dict:
    return {
        'click': click,
        'wait_click': wait_click
    }

def click(image_path: str, confidence: float = 0.9, button: str = 'left') -> bool:
    """Find and click on an image"""
    try:
        confidence = float(confidence)
        location = find(image_path, confidence)
        if location:
            center_x, center_y = pyautogui.center(location)
            pyautogui.click(center_x, center_y, button=button)
            return True
        return False
    except Exception:
        return False

def wait_click(image_path: str, timeout: float = 10, confidence: float = 0.9, button: str = 'left') -> bool:
    """Wait for image to appear and click it"""
    timeout = float(timeout)
    confidence = float(confidence)
    
    location = wait_for(image_path, timeout, confidence)
    if location:
        center_x, center_y = pyautogui.center(location)
        pyautogui.click(center_x, center_y, button=button)
        return True
    return False
