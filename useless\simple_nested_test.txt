# Simple test for nested module function calls

from math import basic

# Define variables
x = 5
y = 10

# Test simple nested module function calls
print Testing simple nested module function calls:

# Test basic.add with nested basic.multiply
result1 = $basic.add($basic.multiply(2, 3), 5)
print 2 * 3 + 5 = $result1

# Test basic.multiply with nested basic.add
result2 = $basic.multiply($basic.add(2, 3), 2)
print (2 + 3) * 2 = $result2

# Test basic.subtract with nested basic.multiply
result3 = $basic.subtract(10, $basic.multiply(2, 3))
print 10 - (2 * 3) = $result3

# Test if statement with nested function calls
if $basic.add($basic.multiply(2, 3), 5) > 10:
    print 2 * 3 + 5 is greater than 10 - This should display

if $basic.subtract(10, $basic.multiply(2, 3)) < 10:
    print 10 - (2 * 3) is less than 10 - This should display
