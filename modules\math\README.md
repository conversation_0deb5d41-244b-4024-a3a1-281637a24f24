# Math Module

A comprehensive collection of mathematical functions for your macros.

## New Feature: Multiple Variable Assignment

The macro system now supports multiple variable assignment with a single line:

```
# Assign multiple variables at once
x1, y1 = 0, 0
x2, y2 = 3, 4

# Use them in calculations
distance = $geometry.distance_2d(x1, y1, x2, y2)
```

This makes your macros more concise and easier to read.

## Modules

### Basic Math (`basic.py`)
- Basic arithmetic operations
- Rounding and absolute value
- Powers and square roots
- Modulo, GCD, LCM, and factorial
- Distance calculation between points

### Advanced Math (`advanced.py`)
- Trigonometric functions (sin, cos, tan)
- Inverse trigonometric functions
- Logarithmic and exponential functions
- Prime number operations
- Fibonacci sequence

### Statistics (`stats.py`)
- Mean, median, mode
- Range, variance, standard deviation
- Percentiles and z-scores
- Correlation
- Min, max, sum, count

### Unit Conversion (`convert.py`)
- Length conversions (cm/inch, feet/meters, etc.)
- Weight/mass conversions (kg/pounds, oz/grams)
- Volume conversions (liters/gallons, ml/fl oz)
- Temperature conversions (C/F/K)
- Speed, area, pressure, digital, and time conversions

### Financial Calculations (`finance.py`)
- Interest calculations (simple and compound)
- Loan calculations (payment, principal, term, rate)
- Present and future value
- ROI, discount, markup, tax, and tip calculations
- Depreciation

### Geometry (`geometry.py`)
- 2D shape calculations (area, perimeter)
- 3D shape calculations (volume, surface area)
- Distance and midpoint calculations
- Angle and slope calculations

## Usage Examples

Import the specific module you need:

```
from math import basic
from math import convert
from math import stats
```

### Basic Math Examples

```
# Add numbers
result = $basic.add(5, 10, 15)
typef "Sum: $result"

# Subtract numbers
result = $basic.subtract(100, 20, 10, 5)
typef "100 - 20 - 10 - 5 = $result"

# Calculate square root
sqrt_result = $basic.sqrt(16)
typef "Square root: $sqrt_result"

# Calculate distance between points
x1, y1 = 0, 0
x2, y2 = 3, 4
dist = $basic.distance(x1, y1, x2, y2)
typef "Distance: $dist"
```

### Conversion Examples

```
# Convert temperature
temp_f = $convert.c_to_f(25)
typef "25°C = $temp_f°F"

# Convert length
inches = $convert.cm_to_inch(30)
typef "30 cm = $inches inches"
```

### Statistics Examples

```
# Calculate mean
data = "10, 15, 20, 25, 30"
avg = $stats.mean(data)
typef "Average: $avg"

# Find the maximum value
max_val = $stats.max(data)
typef "Maximum value: $max_val"
```

### Financial Examples

```
# Calculate monthly loan payment
payment = $finance.loan_payment(200000, 0.04, 30)
typef "Monthly payment: $payment"

# Calculate compound interest
future_val = $finance.compound_interest(10000, 0.05, 10)
typef "Future value: $future_val"
```

### Geometry Examples

```
# Calculate circle area
area = $geometry.circle_area(5)
typef "Circle area: $area"

# Calculate distance between points
dist = $geometry.distance_2d(0, 0, 3, 4)
typef "Distance: $dist"
```
