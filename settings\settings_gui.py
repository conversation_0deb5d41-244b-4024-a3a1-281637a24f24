# Import settings manager to edit settings values 
try:
    from settings import settings_manager as settings
except:
    import settings_manager as settings

# Import themes to show the gui with the right theme
try:
    import themes
except:
    from settings import themes

from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QComboBox, QSpinBox, QLineEdit
import sys

settings_manager = settings.Settings()

class SettingsMenu(QWidget):
    def __init__(self):
        super().__init__()
        self.settings = settings_manager.settings
        self.initUI()

    def initUI(self):
        self.setWindowTitle("Settings")
        self.setGeometry(200, 200, 300, 250)

        # Set theme
        try:
            self.setStyleSheet(themes.get_stylesheet())
        except themes.InvalidThemeError:
            self.settings["theme"] = "Dark"  # Default to Dark theme if invalid
            settings_manager.update(self.settings)
            self.setStyleSheet(themes.get_stylesheet())  # Try again with corrected settings

        layout = QVBoxLayout()

        # Define settings and UI elements in a loop
        settings_items = [
            ("Theme", Q<PERSON>omboBox, ["Dark", "Light", "DarkxRed", "DarkxGreen", "DarkxBlue"], "theme"),
            ("Macro Speed", QSpinBox, (1, 100), "macro_speed"),
            ("Start Key", QLineEdit, None, "start_key"),
            ("Stop Key", QLineEdit, None, "stop_key"),
            ("Font Size", QSpinBox, (8, 32), "font_size")
        ]
        
        # Create widgets dynamically
        self.widgets = {}
        for label, widget_class, range_or_items, key in settings_items:
            layout.addWidget(QLabel(label))
            widget = widget_class()
            if isinstance(widget, QComboBox):
                widget.addItems(range_or_items)
                widget.setCurrentText(self.settings.get(key, range_or_items[0]))
            elif isinstance(widget, QSpinBox):
                widget.setRange(*range_or_items)
                widget.setValue(self.settings.get(key, range_or_items[0]))
            else:
                widget.setText(self.settings.get(key, ""))
            self.widgets[key] = widget
            layout.addWidget(widget)

        # Save button
        self.saveButton = QPushButton("Save Settings")
        self.saveButton.clicked.connect(self.saveSettings)
        layout.addWidget(self.saveButton)

        self.setLayout(layout)

    def saveSettings(self):
        """
        Save settings and update the settings manager.
        """
        for key, widget in self.widgets.items():
            if isinstance(widget, QComboBox):
                self.settings[key] = widget.currentText()
            elif isinstance(widget, QSpinBox):
                self.settings[key] = widget.value()
            else:
                self.settings[key] = widget.text()
        settings_manager.update(self.settings)
        self.close()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = SettingsMenu()
    window.show()
    sys.exit(app.exec_())
