# Test file for nested function calls

# Import modules
from math import basic

# Define variables
x = 5
y = 10

# Test basic module function call
print Testing basic module function call:
result1 = $basic.add(5, 10)
print 5 + 10 = $result1

# Test nested module function call
print Testing nested module function call:
result2 = $basic.add($basic.multiply(2, 3), 4)
print 2 * 3 + 4 = $result2

# Define a function
func double(n):
    return $basic.multiply($n, 2)

# Test user function call
print Testing user function call:
result3 = $double(5)
print double(5) = $result3

# Test nested user function call
print Testing nested user function call:
result4 = $basic.add($double(3), 2)
print double(3) + 2 = $result4

# Define another function that uses the first function
func addDoubled(a, b):
    doubled_a = $double($a)
    doubled_b = $double($b)
    return $basic.add($doubled_a, $doubled_b)

# Test complex nested function calls
print Testing complex nested function calls:
result5 = $addDoubled(3, 4)
print addDoubled(3, 4) = $result5

# Test deeply nested calls
print Testing deeply nested calls:
result6 = $basic.add($double($basic.add(2, 3)), $basic.multiply(3, $double(2)))
print double(2 + 3) + 3 * double(2) = $result6
