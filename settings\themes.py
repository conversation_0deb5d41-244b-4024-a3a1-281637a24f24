try:
    from settings import settings_manager
except:
    import settings_manager
settings_manager = settings_manager.Settings()

class InvalidThemeError(Exception):
    pass

def font_size():
    return settings_manager.settings["font_size"]

def dark_theme():
    return f"""
            QWidget {{
                background-color: #1e1e1e;
                color: #fff;
                font-family: Arial, sans-serif;
            }}
            QPushButton, QCheckBox, QComboBox {{
                background-color: #2e2e2e;
                color: white;
                border-radius: 5px;
                font-size: {font_size()}px;
                padding: 10px;
            }}
            QLineEdit, QTextEdit {{
                background-color: #2e2e2e;
                color: #fff;
                font-size: {font_size()}px;
                padding: 15px;
                border-radius: 10px;
            }}
            QLabel {{
                color: #fff;
                font-size: {font_size()}px;
            }}
            QComboBox {{
                background-color: #2e2e2e;
                color: white;
                font-size: {font_size()}px;
                padding: 10px;
                border-radius: 10px;
            }}
            QSpinBox {{
                background-color: #2e2e2e;
                color: white;
                border-radius: 5px;
                font-size: {font_size()}px;
                padding: 10px;
                selection-background-color: #3DDC84;
            }}
        """

def light_theme():
    return f"""
            QWidget {{
                background-color: #fafafa;
                color: #333;
                font-family: Arial, sans-serif;
            }}
            QPushButton, QCheckBox, QComboBox {{
                background-color: #e5e5e5;
                color: #333;
                border: 1px solid #ccc;
                border-radius: 5px;
                font-size: {font_size()}px;
                padding: 8px 15px;
            }}
            QPushButton:hover {{
                background-color: #d1d1d1;
            }}
            QLineEdit, QTextEdit {{
                background-color: #fff;
                color: #333;
                font-size: {font_size()}px;
                padding: 12px 15px;
                border: 1px solid #ccc;
                border-radius: 10px;
            }}
            QLabel {{
                color: #333;
                font-size: {font_size()}px;
            }}
            QComboBox {{
                background-color: #fff;
                color: #333;
                font-size: {font_size()}px;
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 10px;
            }}
            QSpinBox {{
                background-color: #fff;
                color: #333;
                border: 1px solid #ccc;
                border-radius: 5px;
                font-size: {font_size()}px;
                padding: 8px;
                selection-background-color: #3DDC84;
            }}
        """

def darkx(accent_color):
    return f"""
        QWidget {{
            background-color: #1e1e1e;
            color: #fff;
            font-family: Arial, sans-serif;
        }}
        QPushButton, QCheckBox, QComboBox {{
            background-color: #2e2e2e;
            color: #fff;
            border: 1px solid {accent_color};
            border-radius: 5px;
            font-size: {font_size()}px;
            padding: 10px;
        }}
        QPushButton:hover {{
            background-color: {accent_color};
            color: #fff;
        }}
        QLineEdit, QTextEdit {{
            background-color: #2e2e2e;
            color: #fff;
            font-size: {font_size()}px;
            padding: 15px;
            border: 1px solid {accent_color};
            border-radius: 10px;
        }}
        QLabel {{
            color: #fff;
            font-size: {font_size()}px;
        }}
        QComboBox {{
            background-color: #2e2e2e;
            color: #fff;
            font-size: {font_size()}px;
            padding: 10px;
            border: 1px solid {accent_color};
            border-radius: 10px;
        }}
        QSpinBox {{
            background-color: #2e2e2e;
            color: #fff;
            border-radius: 5px;
            font-size: {font_size()}px;
            padding: 15px;
            selection-background-color: {accent_color};
            border: 1px solid {accent_color};
        }}
    """

def get_stylesheet():
    theme = settings_manager.settings["theme"].lower()
    
    themes = {
        "dark": dark_theme(),
        "light": light_theme(),
        "darkxred": darkx("#ff4f2e"),
        "darkxgreen": darkx("#3DDC84"),
        "darkxblue": darkx("#2e76ff")
    }
    
    if theme in themes:
        return themes[theme]
    
    print("Invalid theme color")
    raise InvalidThemeError()
