# Macro Automation Suite

A powerful toolkit for automating mouse and keyboard actions, featuring a macro player and coordinate picker.

## Table of Contents
- [Macro Automation Suite](#macro-automation-suite)
  - [Table of Contents](#table-of-contents)
  - [Installation](#installation)
  - [Macro Player](#macro-player)
    - [Quick Start](#quick-start)
    - [Running Modes](#running-modes)
      - [GUI Mode](#gui-mode)
      - [Command Line Mode](#command-line-mode)
    - [Commands Reference](#commands-reference)
      - [Mouse Actions](#mouse-actions)
      - [Keyboard Actions](#keyboard-actions)
      - [System Actions](#system-actions)
    - [Functions](#functions)
    - [Tips \& Best Practices](#tips--best-practices)
  - [Pixel Picker](#pixel-picker)
    - [Usage](#usage)
    - [Features](#features)
      - [Coordinate Mode](#coordinate-mode)
      - [RGB Mode](#rgb-mode)
      - [Controls](#controls)
  - [Settings](#settings)
  - [Troubleshooting](#troubleshooting)

## Installation

1. Download the latest release
2. Run the installer
3. Launch either the Macro Player or Pixel Picker from the start menu

## Macro Player

### Quick Start
1. Launch the Macro Player
2. Write or paste commands in the editor
3. Click "Run" or press Page Up to start
4. Press End key or click "Kill" to stop anytime

### Running Modes

#### GUI Mode
- Open the app normally
- Edit commands in real-time
- Run/Stop with buttons or hotkeys
- See command output and errors

#### Command Line Mode
```bash
python main.py -f path/to/macro.txt
```
- Runs macro file directly
- Exits when complete
- Useful for automation scripts

### Commands Reference

#### Mouse Actions
```
click                   # Left click
click right            # Right click
click middle           # Middle click
click double          # Double click
click shift           # Shift + click
click 100 200         # Click at coordinates
click dur 1 100 200   # Click at (100,200) with 1s movement
move 100 200          # Move mouse to coordinates
move dur 1 100 200    # Move to coordinates over 1 second
```

#### Keyboard Actions
```
press a               # Press single key
press enter          # Press enter key
hotkey ctrl c        # Press multiple keys together
type "Hello World"   # Type text instantly
typef 0.1 "Hello"    # Type with 0.1s delay between chars
```

#### System Actions
```
wait 2               # Wait 2 seconds
url google.com       # Open URL in browser
search cats         # Search Google
console dir         # Run console command
python print(2+2)   # Execute Python code
```

### Functions

Create reusable command blocks:
```
func myClick(2) {    # Function with 2 parameters
    move $1 $2       # Use $1, $2 for parameters
    wait 0.5         # Add delay
    click            # Perform click
}

myClick 100 200      # Call the function
```

Function features:
- Up to 9 parameters ($1 through 9)
- Can be nested
- Use comments inside
- Can contain any valid commands

### Tips & Best Practices

1. Testing
   - Start with longer delays (wait commands)
   - Test in small sections
   - Use comments to mark sections
   - Have the Kill button/key ready

2. Coordinates
   - Use Pixel Picker to find exact coordinates
   - Add small delays before clicks
   - Consider screen resolution

3. Reliability
   - Add wait commands between actions
   - Use longer delays for web pages
   - Test edge cases
   - Add error handling with comments

4. Organization
   - Group related actions
   - Comment your macros
   - Use functions for repeated actions
   - Keep a library of useful functions

## Pixel Picker

### Usage
1. Launch Pixel Picker
2. Press Alt+C to capture coordinates and RGB
3. Click "Copy" or use Alt+C (with Auto Copy) to copy

### Features

#### Coordinate Mode
- Shows exact mouse position
- Format: (x, y)
- Perfect for macro coordinates

#### RGB Mode
- Shows pixel color values
- Format: (r, g, b)
- Useful for color checking

#### Controls
- Alt+C: Capture coordinates and RGB
- Alt+T: Toggle between copying coordinates and RGB
- Copy button: Copy displayed value
- Auto Copy: Automatically copy when using Alt+C

## Settings

Access settings through the gear icon:

- Theme: Choose UI theme
  - Dark (default)
  - Light
  - DarkxRed
  - DarkxGreen
  - DarkxBlue

- Macro Speed: Adjust execution speed (1-100) (higher values = faster)
- Start Key: Change macro start key (default: Page Up)
- Stop Key: Change macro stop key (default: End)
- Font Size: Adjust editor text size (8-32)

## Troubleshooting

Common Issues:

1. Macro Not Running
   - Check if another macro is running
   - Verify syntax
   - Try increasing wait times
   - Check coordinates are on screen

2. Incorrect Coordinates
   - Verify screen resolution matches
   - Use Pixel Picker to double-check
   - Consider relative coordinates

3. Keyboard Commands Not Working
   - Check for caps lock
   - Verify key names
   - Try adding delays between keys

4. Application Not Responding
   - Kill the macro with End key
   - Close and restart the application
   - Check system resources

For additional help or bug reports, visit our GitHub repository.
