# This is a test file for syntax highlighting

# Import modules
from math import basic
import keyboard

# Define variables
$x = 10
$y = 20
$result = $basic.add($x, $y)

# Define a function
func calculate(a, b, operation):
    print Performing $operation on $a and $b
    
    # Use conditionals
    if $operation == "add":
        result = $basic.add($a, $b)
    
    if $operation == "subtract":
        result = $basic.subtract($a, $b)
    
    if $operation == "multiply":
        result = $basic.multiply($a, $b)
    
    if $operation == "divide":
        result = $basic.divide($a, $b)
        
    print Result: $result
    return $result

# Test the function
wait 1
print Testing calculate function...
$result1 = $calculate(20, 5, "add")
print Returned value: $result1

# Use commands
click 100, 200
move 300, 400
press enter
type Hello World
wait 2

# Use strings and numbers
$message = "This is a test"
$count = 42
print $message ($count times)
