from running.module_loader import <PERSON><PERSON><PERSON><PERSON>oader

# used to store and call functions
class Functions:
    def __init__(self) -> None:
        self.module_loader = ModuleLoader()
        self.imported_modules = {}  # Store full paths of imported modules

    # used to import modules from the module loader class
    def import_module(self, module_path: str) -> bool:
        """Import a module or package"""
        success = self.module_loader.load_module(module_path)
        if success:
            parts = module_path.split('.')
            self.imported_modules[parts[-1]] = module_path
            # Also store the full path
            self.imported_modules[module_path] = module_path
        return success

    # used to get a list of functions from a module
    def get_module_functions(self, module_path: str) -> list:
        """Get list of function names from a module"""
        if module_path in self.module_loader.modules:
            return list(self.module_loader.modules[module_path].keys())
        return []

    # Helper Functions

    # used for cleaning up user input
    def args_cleaner(self, args):
        cleaned_args = []
        for arg in args:
            if "," in arg or "(" in arg or ")" in arg:
                # Remove parentheses and split by comma if present
                arg = arg.replace("(", "").replace(")", "").replace(",", "")
            if " " in arg:
                # Remove spaces and append the cleaned argument
                arg = arg.replace(" ", "")
                cleaned_args.append(arg)
            else:
                cleaned_args.append(arg)
        return cleaned_args

    # used to convert strings to int or float if possible
    def correct_data_type(self, string):
        # Skip conversion if the input is not a string
        if not isinstance(string, str):
            return string

        # Skip conversion for variables (starting with $) and error messages
        if string.startswith('$') or string.startswith('Error:'):
            return string

        # Try to convert to int if no decimal point
        if not "." in string:
            try:
                string = int(string)
            except:
                pass
        else:
            # Try to convert to float if decimal point present
            try:
                string = float(string)
            except:
                pass
        return string

    # used to call module functions
    def call_module_function(self, module_path: str, func_name: str, *args):
        """Call a function from a loaded module"""
        # Check if we have this module imported
        full_path = self.imported_modules.get(module_path, module_path)

        func = self.module_loader.get_function(full_path, func_name)
        if func:
            try:
                # Convert string arguments to numbers if they look like numbers
                converted_args = []
                for arg in args:
                    converted_args.append(self.correct_data_type(arg))
                result = func(*converted_args)
                # Convert result to string for macro system
                return str(result)
            except Exception as e:
                return f"Error: {str(e)}"
        return f"Error: Function {func_name} not found in module {module_path}"
