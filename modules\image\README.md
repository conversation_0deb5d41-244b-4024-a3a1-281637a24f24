# Image Recognition Module

This module provides advanced image recognition capabilities for your macros.

## Module Structure

The module is organized into multiple files with short, easy-to-type names:

- `__init__.py` - Package marker
- `img.py` - Image capture and pixel functionality
- `find.py` - Image finding and waiting functionality
- `act.py` - Functions that interact with found images (clicking)

## Available Functions

### Basic Image Functions

- `find(image_path, confidence=0.9)` - Find an image on screen and return its location
- `wait(image_path, timeout=10, confidence=0.9)` - Wait for an image to appear on screen
- `click(image_path, confidence=0.9, button='left')` - Find and click on an image
- `wait_click(image_path, timeout=10, confidence=0.9, button='left')` - Wait for an image to appear and click it
- `find_all(image_path, confidence=0.9, limit=None)` - Find all occurrences of an image on screen

### Screenshot Functions

- `screenshot(region=None, filename=None)` - Take a screenshot and save to file
- `pixel(x, y)` - Get RGB color at coordinates
- `region(x, y, width, height, filename=None)` - Capture a specific region to a file

## Usage Examples

Import the modules in your macro:
```
from image import img    # For screenshots and pixel colors
from image import find   # For finding images
from image import act    # For clicking on images
```

### Example 1: Wait for an image and click it

```
# Wait up to 30 seconds for the login button to appear and click it
typef "Waiting for login button..."
press enter
wait_result = $act.wait_click("images/login_button.png", 30)
typef "Click result: $wait_result"
press enter
```

### Example 2: Check if an image exists

```
# Check if the error message appears
if $find.find("images/error_message.png"):
    typef "Error detected!"
    press enter
else:
    typef "No error found."
    press enter
```

### Example 3: Get pixel color

```
# Get color at position (100, 100)
color = $img.pixel(100, 100)
typef "Color at (100, 100): $color"
press enter
```

### Example 4: Find all instances of an image

```
# Find all instances of a button (up to 5)
buttons = $find.find_all("images/button.png", limit=5)
typef "Found $buttons.length buttons"
press enter
```

## Tips for Reliable Image Recognition

1. **Image Quality**
   - Use clear, high-quality images
   - Capture the exact area you want to find
   - Avoid images with transparency

2. **Confidence Level**
   - Default is 0.9 (90% match)
   - Lower for more flexible matching (e.g., 0.7)
   - Higher for exact matching (e.g., 0.95)

3. **Timeouts**
   - Set appropriate timeouts based on expected delays
   - Default is 10 seconds
   - Use longer timeouts for slower applications or web pages

4. **Error Handling**
   - Always check return values
   - Functions return None/False if image not found
   - Use wait with appropriate timeout for dynamic content
