# Test if statements with nested functions

from math import basic

# Define variables
x = 10
y = 5

# Test if statements with nested functions
print Testing if statements with nested functions:

# Test equal comparison
if $basic.add($x, $y) == 15:
    print $x + $y equals 15 - This should display

# Test greater than comparison
result1 = $basic.multiply($x, 2)
result2 = $basic.add($y, 10)
if $result1 > $result2:
    print $x * 2 is greater than $y + 10 - This should display

# Test less than comparison with nested functions
result3 = $basic.subtract($x, $y)
result4 = $basic.multiply($y, 2)
if $result3 < $result4:
    print $x - $y is less than $y * 2 - This should display

# Test condition that should be false
if $basic.add($x, $y) == 20:
    print This should NOT display

# This is not an else block (not supported in this syntax)
print $x + $y does not equal 20 - This should display

# Test nested functions in both sides of comparison
if $basic.multiply($x, 2) == $basic.add($x, 10):
    print $x * 2 equals $x + 10 - This should display
