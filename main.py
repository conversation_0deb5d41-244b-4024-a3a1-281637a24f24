# Imports

# PyQt5 Imports
from PyQt5.QtWidgets import <PERSON>Application, QWidget, QVBoxLayout, QHBoxLayout, QPushButton

# Local Imports
from running.interpreter import Interpreter # Turning the code into actions
from static.pixel_picker import PixelPicker # Coordinates tool
from static.editor import CodeEditor # Specialized code editor
from settings import themes as themes # The theme

# Misc imports
import keyboard # For the start and kill hotkeys
from time import sleep # To stop the run button from being spammed
import sys # For the window
import os # For the kill switch
import argparse # For command line arguments

class MacroApp(QWidget):
    def __init__(self, file_to_run=None):
        QWidget.__init__(self)
        self.interpreter = Interpreter()

        # Emergency kill switch - Ctrl+Alt+K combination for immediate termination (100% reliable and instant)
        keyboard.add_hotkey("ctrl+alt+k", self.emergency_kill, suppress=True)

        # Regular kill switch (softer stop)
        keyboard.add_hotkey("end", self.kill, suppress=True)

        self.initUI()
        keyboard.add_hotkey("page up", self.run, suppress=True)

        # If a file was specified, run it and exit
        if file_to_run:
            try:
                with open(file_to_run, 'r') as file:
                    macro_code = file.read()
                    self.interpreter.full_parse(macro_code)
                    os._exit(0)
            except FileNotFoundError:
                print(f"Error: File '{file_to_run}' not found.")
                os._exit(1)
            except Exception as e:
                print(f"Error running macro: {str(e)}")
                os._exit(1)

    def emergency_kill(self):
        """Emergency kill switch - immediately terminates the entire process"""
        print("EMERGENCY STOP ACTIVATED")
        os._exit(1)  # Immediately terminate the process with no cleanup

    def kill(self):
        """Regular kill switch - attempts to stop gracefully"""
        self.interpreter.kill()
        # Regular kill already calls os._exit(1) in the interpreter

    def open_coords_tool(self):
        """Open the coordinates tool window"""
        self.coords_window = PixelPicker()
        self.coords_window.show()

    def run(self):
        self.interpreter.full_parse(self.editor.toPlainText())
        sleep(0.25)

    def initUI(self):
        # Display emergency kill switch information
        print("==== SAFETY INFORMATION ====\nEMERGENCY KILL SWITCH: Press CTRL+ALT+K to immediately terminate the macro\nUse this to instantly stop the macro\n============================")

        # Set window properties
        self.setWindowTitle('Macro')
        self.setGeometry(100, 100, 700, 500)
        self.setStyleSheet(themes.get_stylesheet())

        # Main layout
        layout = QVBoxLayout()

        # Buttons layout (horizontal)
        buttonLayout = QHBoxLayout()

        # Start Macro Button
        self.startButton = QPushButton('Run', self)
        self.startButton.clicked.connect(self.run)

        # Stop Macro Button
        self.stopButton = QPushButton('Kill', self)
        self.stopButton.clicked.connect(self.kill)

        # Coordinates Tool Button
        self.coordsButton = QPushButton('Pixel Picker', self)
        self.coordsButton.clicked.connect(self.open_coords_tool)

        # Add buttons to the horizontal layout
        buttonLayout.addWidget(self.startButton)
        buttonLayout.addWidget(self.stopButton)
        buttonLayout.addWidget(self.coordsButton)
        layout.addLayout(buttonLayout)  # Add the layout

        # Where the code goes
        self.editor = CodeEditor()
        layout.addWidget(self.editor) # Add the editor

        # Set the layout
        self.setLayout(layout)

def main():
    # Set up command line argument parsing
    parser = argparse.ArgumentParser(description='Macro Runner')
    parser.add_argument('-f', '--file', help='Path to macro text file to run')
    args = parser.parse_args()

    app = QApplication(sys.argv)

    if args.file:
        # File mode - just run the macro and exit
        window = MacroApp(args.file)
    else:
        # GUI mode - show window and run event loop
        window = MacroApp()
        window.show()
        sys.exit(app.exec_())

if __name__ == '__main__':
    main()