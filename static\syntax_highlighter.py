from PyQt5.QtCore import QRegExp, QTimer
from PyQt5.QtGui import QSyntaxHighlighter, QTextCharFormat, QFont, QColor
from settings import settings_manager

class MacroSyntaxHighlighter(QSyntaxHighlighter):
    def __init__(self, document):
        super().__init__(document)
        self.settings = settings_manager.Settings()
        self.highlighting_rules = []

        # Initialize theme colors and setup rules
        theme = self.settings.settings["theme"].lower()
        self.setupThemeColors(theme)
        self.setupHighlightingRules()

    def setupThemeColors(self, theme):
        """Set up color scheme based on current theme"""
        if theme.startswith("dark"):
            # Dark theme colors
            self.command_color = "#569CD6"  # Blue
            self.variable_color = "#9CDCFE"  # Light blue
            self.function_color = "#DCDCAA"  # Yellow
            self.module_color = "#4EC9B0"    # Teal
            self.comment_color = "#6A9955"   # Green
            self.string_color = "#CE9178"    # Orange
            self.number_color = "#B5CEA8"    # Light green
            self.keyword_color = "#C586C0"   # Purple

            # For accent themes
            if "red" in theme:
                self.accent_color = "#ff4f2e"
            elif "green" in theme:
                self.accent_color = "#3DDC84"
            elif "blue" in theme:
                self.accent_color = "#2e76ff"
            else:
                self.accent_color = "#3DDC84"
        else:
            # Light theme colors
            self.command_color = "#0000FF"   # Blue
            self.variable_color = "#001080"  # Dark blue
            self.function_color = "#795E26"  # Brown
            self.module_color = "#267f99"    # Teal
            self.comment_color = "#008000"   # Green
            self.string_color = "#A31515"    # Red
            self.number_color = "#098658"    # Dark green
            self.keyword_color = "#AF00DB"   # Purple
            self.accent_color = "#3DDC84"    # Default accent

    def setupHighlightingRules(self):
        """Set up syntax highlighting rules"""
        self.highlighting_rules = []

        # Note: Comments and strings are handled separately in highlightBlock
        # to ensure other syntax highlighting doesn't occur within them

        # Number format
        number_format = QTextCharFormat()
        number_format.setForeground(QColor(self.number_color))
        self.highlighting_rules.append((QRegExp("\\b[0-9]+\\b"), number_format))

        # Commands format
        command_format = QTextCharFormat()
        command_format.setForeground(QColor(self.command_color))
        command_format.setFontWeight(QFont.Bold)

        # Define command patterns
        commands = [
            "wait", "press", "hold", "release", "click", "rclick", "dclick",
            "mclick", "gotourl", "browser", "copy", "paste", "typef", "type",
            "move", "moveto", "print", "run", "console", "python"
        ]

        # Add each command as a rule
        for command in commands:
            pattern = f"\\b{command}\\b"
            self.highlighting_rules.append((QRegExp(pattern), command_format))

        # Variables format (starts with $)
        variable_format = QTextCharFormat()
        variable_format.setForeground(QColor(self.variable_color))
        variable_pattern = "\\$[a-zA-Z_][a-zA-Z0-9_]*"
        self.highlighting_rules.append((QRegExp(variable_pattern), variable_format))

        # Function keyword format
        function_keyword_format = QTextCharFormat()
        function_keyword_format.setForeground(QColor(self.keyword_color))
        function_keyword_format.setFontWeight(QFont.Bold)
        function_keyword_pattern = "\\bfunc\\b"
        self.highlighting_rules.append((QRegExp(function_keyword_pattern), function_keyword_format))

        # Function name format
        function_name_format = QTextCharFormat()
        function_name_format.setForeground(QColor(self.function_color))
        function_name_format.setFontWeight(QFont.Bold)

        # Add function name pattern to rules
        self.highlighting_rules.append((QRegExp("\\bfunc\\s+([a-zA-Z_][a-zA-Z0-9_]*)\\s*\\("), function_name_format))

        # Module function calls format
        module_func_format = QTextCharFormat()
        module_func_format.setForeground(QColor(self.module_color))
        module_func_pattern = "\\$[a-zA-Z_][a-zA-Z0-9_]*\\.[a-zA-Z_][a-zA-Z0-9_]*\\s*\\("
        self.highlighting_rules.append((QRegExp(module_func_pattern), module_func_format))

        # Keywords format
        keyword_format = QTextCharFormat()
        keyword_format.setForeground(QColor(self.keyword_color))
        keyword_format.setFontWeight(QFont.Bold)

        keywords = ["import", "from", "as", "return", "if", "else", "elif", "while", "for", "in", "func"]
        for keyword in keywords:
            pattern = f"\\b{keyword}\\b"
            self.highlighting_rules.append((QRegExp(pattern), keyword_format))

    def highlightBlock(self, text):
        """Apply highlighting rules to the given block of text"""
        # First, identify comments and strings to avoid highlighting within them
        self.highlightComments(text)
        self.highlightStrings(text)

        # Apply other rules, but skip areas that are already formatted as comments or strings
        for pattern, format in self.highlighting_rules:
            # Skip comment and string patterns as they've already been processed
            if pattern.pattern() == "#.*$" or \
               pattern.pattern() == '"[^"\\\n]*(?:\\.[^"\\\n]*)*"' or \
               pattern.pattern() == "'[^'\\\n]*(?:\\.[^'\\\n]*)*'":
                continue

            expression = QRegExp(pattern)
            index = expression.indexIn(text)
            while index >= 0:
                length = expression.matchedLength()

                # Check if this region is already formatted as a comment or string
                is_special_format = False
                for i in range(index, index + length):
                    if self.format(i).foreground().color().name() in [
                        QColor(self.comment_color).name(),
                        QColor(self.string_color).name()
                    ]:
                        is_special_format = True
                        break

                # Only apply format if not in a comment or string
                if not is_special_format:
                    self.setFormat(index, length, format)

                index = expression.indexIn(text, index + length)

        # Handle multi-line constructs
        self.highlightMultilineFunction(text)

    def highlightComments(self, text):
        """Highlight comments in the text"""
        comment_format = QTextCharFormat()
        comment_format.setForeground(QColor(self.comment_color))
        comment_format.setFontItalic(True)

        # Find comments (anything after #)
        expression = QRegExp("#.*$")
        index = expression.indexIn(text)
        if index >= 0:
            length = expression.matchedLength()
            self.setFormat(index, length, comment_format)

    def highlightStrings(self, text):
        """Highlight strings in the text"""
        string_format = QTextCharFormat()
        string_format.setForeground(QColor(self.string_color))

        # Double-quoted strings
        expression = QRegExp('"[^"\\\n]*(?:\\.[^"\\\n]*)*"')
        index = expression.indexIn(text)
        while index >= 0:
            length = expression.matchedLength()
            self.setFormat(index, length, string_format)
            index = expression.indexIn(text, index + length)

        # Single-quoted strings
        expression = QRegExp("'[^'\\\n]*(?:\\.[^'\\\n]*)*'")
        index = expression.indexIn(text)
        while index >= 0:
            length = expression.matchedLength()
            self.setFormat(index, length, string_format)
            index = expression.indexIn(text, index + length)

    def highlightMultilineFunction(self, text):
        """Handle multi-line function definitions"""
        # States: 0 = normal, 1 = inside function definition

        # Get previous block state
        previous_state = self.previousBlockState()
        if previous_state == -1:
            previous_state = 0

        # Check if we're starting a function definition
        if "func " in text and "(" in text:
            # Set state to inside function
            self.setCurrentBlockState(1)
        elif previous_state == 1 and text.strip().startswith("return "):
            # Format return statement, but only if not in a comment or string
            return_format = QTextCharFormat()
            return_format.setForeground(QColor(self.keyword_color))
            return_format.setFontWeight(QFont.Bold)

            return_pattern = QRegExp("return\\b")
            index = return_pattern.indexIn(text)
            if index >= 0:
                # Check if this region is already formatted as a comment or string
                is_special_format = False
                for i in range(index, index + return_pattern.matchedLength()):
                    if self.format(i).foreground().color().name() in [
                        QColor(self.comment_color).name(),
                        QColor(self.string_color).name()
                    ]:
                        is_special_format = True
                        break

                # Only apply format if not in a comment or string
                if not is_special_format:
                    self.setFormat(index, return_pattern.matchedLength(), return_format)

            # Continue function state
            self.setCurrentBlockState(1)
        elif previous_state == 1 and not text.strip():
            # Empty line inside function
            self.setCurrentBlockState(1)
        elif previous_state == 1 and not text.startswith("    "):
            # No longer indented - end of function
            self.setCurrentBlockState(0)
        elif previous_state == 1:
            # Still inside function
            self.setCurrentBlockState(1)
        else:
            # Normal state
            self.setCurrentBlockState(0)

    def throttledRehighlight(self):
        """Throttle rehighlighting for better performance with large files"""
        if not hasattr(self, '_rehighlight_timer'):
            self._rehighlight_timer = QTimer()
            self._rehighlight_timer.setSingleShot(True)
            self._rehighlight_timer.timeout.connect(self.rehighlight)

        self._rehighlight_timer.start(100)  # Wait 100ms before rehighlighting
