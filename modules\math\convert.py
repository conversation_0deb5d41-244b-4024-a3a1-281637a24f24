"""
Unit conversion module.
"""

MODULE_NAME = "math.convert"

def register_functions():
    return {
        # Length conversions
        'cm_to_inch': cm_to_inch,
        'inch_to_cm': inch_to_cm,
        'feet_to_meters': feet_to_meters,
        'meters_to_feet': meters_to_feet,
        'km_to_miles': km_to_miles,
        'miles_to_km': miles_to_km,
        
        # Weight/Mass conversions
        'kg_to_pounds': kg_to_pounds,
        'pounds_to_kg': pounds_to_kg,
        'oz_to_grams': oz_to_grams,
        'grams_to_oz': grams_to_oz,
        
        # Volume conversions
        'liters_to_gallons': liters_to_gallons,
        'gallons_to_liters': gallons_to_liters,
        'ml_to_oz': ml_to_fluid_oz,
        'oz_to_ml': fluid_oz_to_ml,
        
        # Temperature conversions
        'c_to_f': celsius_to_fahrenheit,
        'f_to_c': fahrenheit_to_celsius,
        'c_to_k': celsius_to_kelvin,
        'k_to_c': kelvin_to_celsius,
        'f_to_k': fahrenheit_to_kelvin,
        'k_to_f': kelvin_to_fahrenheit,
        
        # Speed conversions
        'kmh_to_mph': kmh_to_mph,
        'mph_to_kmh': mph_to_kmh,
        'ms_to_kmh': ms_to_kmh,
        'kmh_to_ms': kmh_to_ms,
        
        # Area conversions
        'sqm_to_sqft': square_meters_to_square_feet,
        'sqft_to_sqm': square_feet_to_square_meters,
        'acres_to_hectares': acres_to_hectares,
        'hectares_to_acres': hectares_to_acres,
        
        # Pressure conversions
        'pa_to_psi': pascal_to_psi,
        'psi_to_pa': psi_to_pascal,
        'bar_to_psi': bar_to_psi,
        'psi_to_bar': psi_to_bar,
        
        # Digital conversions
        'bytes_to_kb': bytes_to_kilobytes,
        'kb_to_mb': kilobytes_to_megabytes,
        'mb_to_gb': megabytes_to_gigabytes,
        'gb_to_tb': gigabytes_to_terabytes,
        
        # Time conversions
        'seconds_to_minutes': seconds_to_minutes,
        'minutes_to_hours': minutes_to_hours,
        'hours_to_days': hours_to_days,
        'days_to_weeks': days_to_weeks
    }

# Length conversions
def cm_to_inch(cm):
    """Convert centimeters to inches"""
    return float(cm) * 0.393701

def inch_to_cm(inches):
    """Convert inches to centimeters"""
    return float(inches) * 2.54

def feet_to_meters(feet):
    """Convert feet to meters"""
    return float(feet) * 0.3048

def meters_to_feet(meters):
    """Convert meters to feet"""
    return float(meters) * 3.28084

def km_to_miles(km):
    """Convert kilometers to miles"""
    return float(km) * 0.621371

def miles_to_km(miles):
    """Convert miles to kilometers"""
    return float(miles) * 1.60934

# Weight/Mass conversions
def kg_to_pounds(kg):
    """Convert kilograms to pounds"""
    return float(kg) * 2.20462

def pounds_to_kg(pounds):
    """Convert pounds to kilograms"""
    return float(pounds) * 0.453592

def oz_to_grams(oz):
    """Convert ounces to grams"""
    return float(oz) * 28.3495

def grams_to_oz(grams):
    """Convert grams to ounces"""
    return float(grams) * 0.035274

# Volume conversions
def liters_to_gallons(liters):
    """Convert liters to gallons (US)"""
    return float(liters) * 0.264172

def gallons_to_liters(gallons):
    """Convert gallons (US) to liters"""
    return float(gallons) * 3.78541

def ml_to_fluid_oz(ml):
    """Convert milliliters to fluid ounces (US)"""
    return float(ml) * 0.033814

def fluid_oz_to_ml(fl_oz):
    """Convert fluid ounces (US) to milliliters"""
    return float(fl_oz) * 29.5735

# Temperature conversions
def celsius_to_fahrenheit(celsius):
    """Convert Celsius to Fahrenheit"""
    return (float(celsius) * 9/5) + 32

def fahrenheit_to_celsius(fahrenheit):
    """Convert Fahrenheit to Celsius"""
    return (float(fahrenheit) - 32) * 5/9

def celsius_to_kelvin(celsius):
    """Convert Celsius to Kelvin"""
    return float(celsius) + 273.15

def kelvin_to_celsius(kelvin):
    """Convert Kelvin to Celsius"""
    return float(kelvin) - 273.15

def fahrenheit_to_kelvin(fahrenheit):
    """Convert Fahrenheit to Kelvin"""
    return celsius_to_kelvin(fahrenheit_to_celsius(fahrenheit))

def kelvin_to_fahrenheit(kelvin):
    """Convert Kelvin to Fahrenheit"""
    return celsius_to_fahrenheit(kelvin_to_celsius(kelvin))

# Speed conversions
def kmh_to_mph(kmh):
    """Convert kilometers per hour to miles per hour"""
    return float(kmh) * 0.621371

def mph_to_kmh(mph):
    """Convert miles per hour to kilometers per hour"""
    return float(mph) * 1.60934

def ms_to_kmh(ms):
    """Convert meters per second to kilometers per hour"""
    return float(ms) * 3.6

def kmh_to_ms(kmh):
    """Convert kilometers per hour to meters per second"""
    return float(kmh) / 3.6

# Area conversions
def square_meters_to_square_feet(sqm):
    """Convert square meters to square feet"""
    return float(sqm) * 10.7639

def square_feet_to_square_meters(sqft):
    """Convert square feet to square meters"""
    return float(sqft) * 0.092903

def acres_to_hectares(acres):
    """Convert acres to hectares"""
    return float(acres) * 0.404686

def hectares_to_acres(hectares):
    """Convert hectares to acres"""
    return float(hectares) * 2.47105

# Pressure conversions
def pascal_to_psi(pascal):
    """Convert pascals to pounds per square inch (PSI)"""
    return float(pascal) * 0.000145038

def psi_to_pascal(psi):
    """Convert pounds per square inch (PSI) to pascals"""
    return float(psi) * 6894.76

def bar_to_psi(bar):
    """Convert bar to pounds per square inch (PSI)"""
    return float(bar) * 14.5038

def psi_to_bar(psi):
    """Convert pounds per square inch (PSI) to bar"""
    return float(psi) * 0.0689476

# Digital conversions
def bytes_to_kilobytes(bytes_val):
    """Convert bytes to kilobytes"""
    return float(bytes_val) / 1024

def kilobytes_to_megabytes(kb):
    """Convert kilobytes to megabytes"""
    return float(kb) / 1024

def megabytes_to_gigabytes(mb):
    """Convert megabytes to gigabytes"""
    return float(mb) / 1024

def gigabytes_to_terabytes(gb):
    """Convert gigabytes to terabytes"""
    return float(gb) / 1024

# Time conversions
def seconds_to_minutes(seconds):
    """Convert seconds to minutes"""
    return float(seconds) / 60

def minutes_to_hours(minutes):
    """Convert minutes to hours"""
    return float(minutes) / 60

def hours_to_days(hours):
    """Convert hours to days"""
    return float(hours) / 24

def days_to_weeks(days):
    """Convert days to weeks"""
    return float(days) / 7
