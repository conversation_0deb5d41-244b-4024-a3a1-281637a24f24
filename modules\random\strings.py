import random
import string
import lorem as lorem

MODULE_NAME = "random.strings"

def register_functions():
    return {
        'letters': random_string,
        'password': generate_password,
        'word': random_word,
        'sentence': random_sentence,
        'paragraph': random_paragraph,
        'email': random_email,
        'username': random_username
    }

def random_string(length=10, charset="letters"):
    """Generate random string with specified charset
    Charsets: letters, digits, special, all"""
    charsets = {
        "letters": string.ascii_letters,
        "digits": string.digits,
        "special": string.punctuation,
        "all": string.ascii_letters + string.digits + string.punctuation
    }
    chars = charsets.get(charset, charsets["all"])
    return ''.join(random.choice(chars) for _ in range(length))

def generate_password(length=12, include_special=True):
    """Generate secure password"""
    chars = string.ascii_letters + string.digits
    if include_special:
        chars += string.punctuation
    return ''.join(random.choice(chars) for _ in range(length))

def random_word():
    """Generate random word"""
    return lorem.word()

def random_sentence(words=6):
    """Generate random sentence"""
    return lorem.sentence()

def random_paragraph(sentences=3):
    """Generate random paragraph"""
    return lorem.paragraph()

def random_email():
    """Generate random email address"""
    domains = ['gmail.com', 'yahoo.com', 'hotmail.com']
    name = random_string(8, "letters").lower()
    domain = random.choice(domains)
    return f"{name}@{domain}"

def random_username():
    """Generate random username"""
    prefixes = ['user', 'cool', 'cyber', 'tech', 'dev']
    suffix = random_string(4, "digits")
    return f"{random.choice(prefixes)}_{suffix}"

