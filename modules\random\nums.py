import random as rand
from typing import Dict, Any
import uuid
import time

MODULE_NAME = "random.nums"

def register_functions() -> Dict[str, Any]:
    return {
        'int': random_int,
        'float': random_float,
        'range': random_range,
        'choice': random_choice,
        'range_float': random_range_float,
        'uuid': random_uuid,
        'timestamp': timestamp
    }

def random_int(min_val=0, max_val=100) -> int:
    """Generate random integer between min and max (inclusive)"""
    return rand.randint(min_val, max_val)

def random_float(min_val=0, max_val=1, decimals=None) -> float:
    """Generate random float between min and max with optional decimal precision"""
    result = min_val + (rand.random() * (max_val - min_val))
    if decimals is not None:
        return round(result, decimals)
    return result

def random_range(start, stop, step=1) -> int:
    """Pick random number from range with step"""
    return rand.choice(range(start, stop, step))

def random_choice(*numbers) -> Any:
    """Pick random number from provided numbers"""
    return rand.choice(numbers)

def random_range_float(start, stop, step=1, decimals=None) -> float:
    """Pick random float from range with step"""
    numbers = [n + (rand.random() * step) for n in range(start, stop, step)]
    result = rand.choice(numbers)
    if decimals is not None:
        return round(result, decimals)
    return result

def random_uuid() -> str:
    """Generate a random UUID"""
    return str(uuid.uuid4())

def timestamp(format="%Y-%m-%d %H:%M:%S") -> str:
    """Generate current timestamp in specified format
    Default format: YYYY-MM-DD HH:MM:SS"""
    return time.strftime(format)

