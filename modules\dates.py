from datetime import datetime, timedelta
import random

MODULE_NAME = "dates"

def register_functions():
    return {
        'now': current_date,
        'random_date': random_date,
        'add_days': add_days,
        'add_months': add_months,
        'format': format_date,
        'difference': date_difference,
        'is_weekend': is_weekend,
        'is_future': is_future
    }

def current_date(format="%Y-%m-%d"):
    """Get current date in specified format"""
    return datetime.now().strftime(format)

def random_date(start_year=2000, end_year=None):
    """Generate random date between start_year and current year"""
    if end_year is None:
        end_year = datetime.now().year
    
    start = datetime(start_year, 1, 1)
    end = datetime(end_year, 12, 31)
    delta = end - start
    random_days = random.randint(0, delta.days)
    return (start + timedelta(days=random_days)).strftime("%Y-%m-%d")

def add_days(date_str, days, format="%Y-%m-%d"):
    """Add/subtract days from date"""
    date = datetime.strptime(date_str, format)
    new_date = date + timedelta(days=days)
    return new_date.strftime(format)

def add_months(date_str, months, format="%Y-%m-%d"):
    """Add/subtract months from date"""
    date = datetime.strptime(date_str, format)
    year = date.year + ((date.month + months - 1) // 12)
    month = ((date.month + months - 1) % 12) + 1
    return date.replace(year=year, month=month).strftime(format)

def format_date(date_str, input_format, output_format):
    """Convert date between formats"""
    date = datetime.strptime(date_str, input_format)
    return date.strftime(output_format)

def date_difference(date1, date2, unit="days"):
    """Get difference between dates in specified unit"""
    d1 = datetime.strptime(date1, "%Y-%m-%d")
    d2 = datetime.strptime(date2, "%Y-%m-%d")
    diff = d2 - d1
    return diff.days

def is_weekend(date_str=current_date()):
    """Check if date is weekend"""
    date = datetime.strptime(date_str, "%Y-%m-%d")
    return date.weekday() >= 5

def is_future(date_str):
    """Check if date is in future"""
    date = datetime.strptime(date_str, "%Y-%m-%d")
    return date > datetime.now()