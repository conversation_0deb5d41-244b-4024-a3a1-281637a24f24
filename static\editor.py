from PyQt5.QtWidgets import QPlainTextEdit, QWidget, QTextEdit
from PyQt5.QtGui import QColor, QPainter, QTextFormat, QFont, QTextCursor
from PyQt5.QtCore import Qt, QRect, QSize
import re

# Import theme system
from settings import settings_manager

# Import custom syntax highlighter
from static.syntax_highlighter import MacroSyntaxHighlighter

# Get settings instance
settings = settings_manager.Settings()

class LineNumberArea(QWidget):
    def __init__(self, editor):
        super().__init__(editor)
        self.editor = editor

    def sizeHint(self):
        return self.editor.lineNumberAreaSize()

    def paintEvent(self, event):
        self.editor.lineNumberAreaPaintEvent(event)


class CodeEditor(QPlainTextEdit):
    def __init__(self):
        super().__init__()
        # Get font size from settings
        font_size = settings.settings["font_size"]
        self.setFont(QFont("Consolas", font_size))

        # Apply theme-based styling
        self.applyTheme()

        # Set up line number area
        self.lineNumberArea = LineNumberArea(self)
        self.blockCountChanged.connect(self.updateLineNumberAreaWidth)
        self.updateRequest.connect(self.updateLineNumberArea)
        self.cursorPositionChanged.connect(self.highlightCurrentLine)

        self.updateLineNumberAreaWidth()
        self.highlightCurrentLine()

        # Initialize syntax highlighter
        self.highlighter = MacroSyntaxHighlighter(self.document())

    def applyTheme(self):
        """Apply the current theme to the editor"""
        theme = settings.settings["theme"].lower()

        # Define colors based on theme
        if theme.startswith("dark"):
            # Dark theme colors
            self.bg_color = "#1e1e1e"  # Background color
            self.text_color = "#dcdcdc"  # Text color
            self.line_number_bg = "#2e2e2e"  # Line number background
            self.line_number_fg = "#8f8f8f"  # Line number text
            self.current_line_color = "#2d2d2d"  # Current line highlight

            # For accent themes (darkxred, darkxgreen, darkxblue)
            if "red" in theme:
                self.accent_color = "#ff4f2e"
            elif "green" in theme:
                self.accent_color = "#3DDC84"
            elif "blue" in theme:
                self.accent_color = "#2e76ff"
            else:
                self.accent_color = "#3DDC84"  # Default accent
        else:
            # Light theme colors
            self.bg_color = "#fafafa"  # Background color
            self.text_color = "#333333"  # Text color
            self.line_number_bg = "#f0f0f0"  # Line number background
            self.line_number_fg = "#999999"  # Line number text
            self.current_line_color = "#f5f5f5"  # Current line highlight
            self.accent_color = "#3DDC84"  # Default accent

        # Apply the styles
        self.setStyleSheet(f"background-color: {self.bg_color}; color: {self.text_color}; border-radius: 10px;")

        # Update syntax highlighter with new theme if it exists
        if hasattr(self, 'highlighter'):
            self.highlighter.setupThemeColors(theme)
            self.highlighter.setupHighlightingRules()
            self.highlighter.rehighlight()  # Force rehighlight of the entire document

    def keyPressEvent(self, event):
        cursor = self.textCursor()

        # TAB: Insert 4 spaces
        if event.key() == Qt.Key_Tab:
            cursor.insertText("    ")  # Insert 4 spaces

        # SHIFT+TAB: Remove 4 spaces (Unindent)
        elif event.key() == Qt.Key_Backtab:
            cursor.movePosition(QTextCursor.StartOfBlock, QTextCursor.KeepAnchor)
            text = cursor.selectedText()

            if text.startswith("    "):  # Remove 4 spaces
                cursor.removeSelectedText()
                cursor.insertText(text[4:])
            else:
                cursor.removeSelectedText()
                cursor.insertText(text.lstrip())

        # BACKSPACE: Remove 4 spaces at a time if possible
        elif event.key() == Qt.Key_Backspace:
            cursor.movePosition(QTextCursor.Left, QTextCursor.KeepAnchor, 4)
            text = cursor.selectedText()

            if text == "    ":  # If exactly 4 spaces, remove them at once
                cursor.removeSelectedText()
            else:
                super().keyPressEvent(event)  # Default backspace behavior

        # ENTER: Auto-indent the next line
        elif event.key() == Qt.Key_Return:
            cursor = self.textCursor()
            # Get current line's indentation
            block_text = cursor.block().text()
            indent_match = re.match(r'^\s*', block_text)
            indent = indent_match.group() if indent_match else ""
            # Insert new line with preserved indentation
            super().keyPressEvent(event)
            self.insertPlainText(indent)
        else:
            super().keyPressEvent(event)  # Default behavior for other keys

    def lineNumberAreaSize(self):
        digits = len(str(max(1, self.blockCount())))
        return QSize(10 + self.fontMetrics().horizontalAdvance('9') * digits, 0)

    def updateLineNumberAreaWidth(self):
        self.setViewportMargins(self.lineNumberAreaSize().width(), 0, 0, 0)

    def updateLineNumberArea(self, rect, dy):
        if dy:
            self.lineNumberArea.scroll(0, dy)
        else:
            self.lineNumberArea.update(0, rect.y(), self.lineNumberArea.width(), rect.height())
        if rect.contains(self.viewport().rect()):
            self.updateLineNumberAreaWidth()

    def resizeEvent(self, event):
        super().resizeEvent(event)
        rect = self.contentsRect()
        self.lineNumberArea.setGeometry(QRect(rect.left(), rect.top(), self.lineNumberAreaSize().width(), rect.height()))

    def lineNumberAreaPaintEvent(self, event):
        painter = QPainter(self.lineNumberArea)
        # Use theme-based colors for line number area
        painter.fillRect(event.rect(), QColor(self.line_number_bg))

        block = self.firstVisibleBlock()
        blockNumber = block.blockNumber()
        top = self.blockBoundingGeometry(block).translated(self.contentOffset()).top()
        bottom = top + self.blockBoundingRect(block).height()

        while block.isValid() and top <= event.rect().bottom():
            if block.isVisible() and bottom >= event.rect().top():
                number = str(blockNumber + 1)
                # Use theme-based color for line numbers
                painter.setPen(QColor(self.line_number_fg))
                painter.drawText(0, int(top), self.lineNumberArea.width() - 5, int(self.fontMetrics().height()),
                                 Qt.AlignRight, number)
            block = block.next()
            top = bottom
            bottom = top + self.blockBoundingRect(block).height()
            blockNumber += 1

    def highlightCurrentLine(self):
        extraSelections = []
        if not self.isReadOnly():
            selection = QTextEdit.ExtraSelection()
            # Use theme-based color for current line highlighting
            lineColor = QColor(self.current_line_color)
            selection.format.setBackground(lineColor)
            selection.format.setProperty(QTextFormat.FullWidthSelection, True)
            selection.cursor = self.textCursor()
            selection.cursor.clearSelection()
            extraSelections.append(selection)
        self.setExtraSelections(extraSelections)
