# Image Recognition Module Changelog

## Version 1.2.0

### Module Restructuring
- Organized the module into multiple files with short, easy-to-type names:
  - `img.py` - Image capture and pixel functionality
  - `find.py` - Image finding and waiting functionality
  - `act.py` - Functions that interact with found images (clicking)
- Added type conversion for parameters to improve robustness
- Improved error handling in all functions

## Version 1.1.0

### Function Name Simplification
- Renamed all functions to be more concise and user-friendly:
  - `save_screenshot()` → `screenshot()`
  - `get_pixel_color()` → `pixel()`
  - `find_image()` → `find()`
  - `wait_for_image()` → `wait()`
  - `click_image()` → `click()`
  - `wait_and_click_image()` → `wait_click()`
  - `find_all_images()` → `find_all()`
  - `capture_region_to_file()` → `region()`

### New Features
- Added `find_all()` function to locate multiple instances of an image
- Improved error handling in all functions
- Enhanced wait functionality with better timeout handling

## Version 1.0.0

### Initial Features
- Basic image finding with `find_image()`
- Screenshot capture with `save_screenshot()`
- Pixel color detection with `get_pixel_color()`
