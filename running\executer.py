import webbrowser
import pyautogui
pyautogui.PAUSE = 0.0
import time
import re
import os

class Executer:
    def __init__(self, parent, pause: float = 0.0) -> None:
        # the delay after everything
        self.pause = pause # (referanced indirectly only in self.pause_wrapper)

        self.parent = parent  # Store reference to the parent interpreter
        self.error = parent.error

    # Helper Functions

    # used to turn strings into floating point numbers
    def numberfo(self, *args: str):
        numbers = []
        for arg in args:
            try:
                number = float(arg)
                numbers.append(number)
            except ValueError:
                return False
        return numbers

    # used to turn strings into integers
    def numberfy(self, *args: str):
        numbers = []
        for arg in args:
            try:
                number = int(arg)
                numbers.append(number)
            except ValueError:
                return False
        return numbers

    # used for cleaning up user input
    def args_cleaner(self, args):
        cleaned_args = []
        for arg in args:
            if "," in arg or "(" in arg or ")" in arg:
                # Remove parentheses and split by comma if present
                arg = arg.replace("(", "").replace(")", "").replace(",", "")
            if " " in arg:
                # Remove spaces and append the cleaned argument
                arg = arg.replace(" ", "")
                cleaned_args.append(arg)
            else:
                cleaned_args.append(arg)
        return cleaned_args

    # finds all the stuff in double qoutes in a string
    def find_quotes(self, text: str):
        # Match both single and double quoted content
        matches = re.findall(r'["\']([^"\']*)["\']', text)
        if matches:
            return matches
        return ""

    # a decorator that adds the global delay to every function
    @staticmethod
    def pause_wrapper(func):
        def wrapper(*args, **kwargs):
            self_instance = args[0]
            result = func(*args, **kwargs)
            time.sleep(self_instance.pause)
            return result
        return wrapper

    # Instant Kill Switch
    def stop(self) -> None:
        os._exit(1)

    # Commands

    # can do any type of click
    @pause_wrapper
    def click(self, args: list[str] = None): # type: ignore
        if args is None:
            args = []
        args = self.args_cleaner(args)

        click_action_map = {
            "middle": pyautogui.middleClick,
            "middleclick": pyautogui.middleClick,
            "mclick": pyautogui.middleClick,
            "double": pyautogui.doubleClick,
            "triple": pyautogui.tripleClick,
        }
        button_map = {
            "left": "left",
            "right": "right",
            "middle": "middle"
        }
        modifier_keyword_map = {
            "shift": "shift",
            "dshift": "shift",
            "doubleshift": "shift",
            "ctrl": "ctrl",
            "control": "ctrl",
            "alt": "alt",
        }

        x, y, dur, button, click_action = None, None, None, None, None
        active_modifiers = set() # Store unique modifiers
        is_relative = False

        # Parse duration if present
        if len(args) >= 2 and args[0] in ["dur", "duration", "time", "delay"]:
            dur_val = self.numberfo(args[1])
            if dur_val is False:
                self.error("Click Command Error", "Invalid duration value.")
                return
            dur = dur_val[0]
            args = args[2:] # Remove duration arguments for further parsing

        # Parse relative flag if present (after duration, so indices align)
        if len(args) >= 1 and args[0] in ["rel", "relative", "delta", "offset"]:
            is_relative = True
            args = args[1:] # Remove relative flag for further parsing

        # Parse coordinates (x, y) if they are numbers and are the last arguments
        if len(args) >= 2 and self.numberfy(args[-2], args[-1]) is not False:
            nums = self.numberfy(args[-2], args[-1])
            x, y = nums[0], nums[1]
            args = args[:-2] # Remove coordinates for further parsing

        # Now, process remaining args for button/click type/modifiers
        for arg in args:
            if arg in button_map:
                button = button_map[arg]
            elif arg in click_action_map:
                click_action = click_action_map[arg]
            elif arg in modifier_keyword_map:
                active_modifiers.add(modifier_keyword_map[arg]) # Add to set
            else:
                self.error("Click Command Error", f"Unrecognized argument or syntax: '{arg}'.")
                return

        # --- Execute the click based on parsed parameters ---
        try:
            for mod in active_modifiers:
                pyautogui.keyDown(mod)

            click_kwargs = {}
            if x is not None and y is not None:
                if is_relative:
                    current_x, current_y = pyautogui.position()
                    x += current_x
                    y += current_y
                click_kwargs['x'] = x
                click_kwargs['y'] = y

            if dur is not None:
                click_kwargs['duration'] = dur
            if button:
                click_kwargs['button'] = button

            # Handle different click actions
            if click_action == pyautogui.doubleClick:
                if button and (x is not None or y is not None): # If button and coordinates, pass both
                    pyautogui.doubleClick(button=button, x=x, y=y, duration=dur)
                elif button: # Only button
                    pyautogui.doubleClick(button=button, duration=dur)
                elif (x is not None or y is not None): # Only coordinates
                     pyautogui.doubleClick(x=x, y=y, duration=dur)
                else: # No button or coordinates
                    pyautogui.doubleClick(duration=dur)

            elif click_action == pyautogui.tripleClick: # Handle triple click
                if button and (x is not None or y is not None):
                     pyautogui.tripleClick(button=button, x=x, y=y, duration=dur)
                elif button:
                    pyautogui.tripleClick(button=button, duration=dur)
                elif (x is not None or y is not None):
                    pyautogui.tripleClick(x=x, y=y, duration=dur)
                else:
                    pyautogui.tripleClick(duration=dur)

            elif click_action == pyautogui.middleClick:
                if button and button != 'middle': # MiddleClick does not take a button argument other than itself
                    self.error("Click Command Error", "Middle click does not support specifying an additional button.")
                    return
                # PyAutoGUI's middleClick can take x, y, duration directly
                pyautogui.middleClick(**click_kwargs)
            else: # Default is single click
                # pyautogui.click can take clicks=1, but single click is the default
                pyautogui.click(**click_kwargs)

        finally: # Ensure keyUp is called even if an error occurs during click
            for mod in active_modifiers:
                pyautogui.keyUp(mod)

    # move to a spot on the screen
    @pause_wrapper
    def move(self, args: list[str] = []):
        args = self.args_cleaner(args)
        match len(args):

            # Move to a specific spot
            case 2:
                if self.numberfy(args[0], args[1]) != False:
                    x, y = self.numberfy(args[0], args[1]) # type: ignore
                    pyautogui.moveTo(x, y)

                # Error
                else:
                    self.error("Move Command Error", "Please enter 2 numbers with no other characters for the x and y.")

            # Move relative to where the mouse already is
            case 3:
                if args[0] in ["rel", "relative", "delta", "offset"]:
                    try:
                        x, y = self.numberfy(args[1], args[2]) # type: ignore
                        pyautogui.moveRel(x, y)
                    except (ValueError, TypeError):
                        self.error("Move Command Error", "Please enter 2 numbers with no other characters for the x and y.")

            # Move the mouse to a specific spot over a certain amount time
            case 4:
                if args[0] in ["dur", "duration", "time", "delay"]:
                    try:
                        x, y = self.numberfy(args[2], args[3]) # type: ignore
                        dur = self.numberfo(args[1])
                        if not dur:
                            dur = 0
                        pyautogui.moveTo(x=x, y=y, duration=dur)
                    except (ValueError, TypeError):
                        self.error("Click Command Error", "Please enter 2 numbers with no other characters for the x and y.")

    # just types text
    @pause_wrapper
    def type_(self, text):
        pyautogui.typewrite(text)

    # types whatever is in double qoutes
    @pause_wrapper
    def typef_(self, text):
        init_text = text
        text = self.find_quotes(text)
        text = "".join(text)
        if len(init_text.split()) > 1:
            if self.numberfo(init_text.split()[0]):
                pyautogui.typewrite(text, interval=self.numberfo(init_text.split()[0])[0]) # type: ignore
            else:
                pyautogui.typewrite(text)
        else:
            pyautogui.typewrite(text)

    # wait a cetain amount of time
    @pause_wrapper
    def wait(self, duration):
        if len(duration) > 0:
            duration = self.numberfo(duration[0])[0] # type: ignore
            print(duration, type(duration))
            try:
                time.sleep(duration)
            except:
                self.error("Wait Command Error", "Please enter a rational number.")
        else:
            self.error("Wait Command Error", "Please enter a rational number.")

    # press a key das it
    @pause_wrapper
    def press(self, key):
        key = key[0]
        try:
            pyautogui.press(key)
        except:
            self.error("Press Command Error", f"'{key}' is not a valid key. Please enter a valid key.")

    # press multiple keys at the same time
    @pause_wrapper
    def hotkey(self, keys):
        try:
            pyautogui.hotkey(keys)
        except:
            self.error("Hotkey Command Error", "Please enter valid keys.")

    # search something with google
    @pause_wrapper
    def search(self, args: list[str]):
        search_text = "+".join(args)
        webbrowser.open(f"https://www.google.com/search?q={search_text}")

    # put a url in the browser
    @pause_wrapper
    def url(self, args: list[str]):
        webbrowser.open("".join(args))

    # run a command in console
    @pause_wrapper
    def console_(self, text:str):
        if os.system(text) != 0:
            self.error("Console Command Error", "The console command didn't return 0.")

    # print text to the console
    @pause_wrapper
    def print_(self, text):
        """Print text to the console

        This command prints text directly to the console without typing it.
        It's useful for debugging or displaying information without simulating keystrokes.

        Usage:
            print Hello, world!
            print $variable_name
            print The value is: $value
        """
        if isinstance(text, list):
            # Join all arguments into a single string
            text = ' '.join(text)

        # Remove quotes if they're surrounding the entire text
        if (text.startswith('"') and text.endswith('"')) or \
           (text.startswith('\'') and text.endswith('\'')):
            text = text[1:-1]

        # Remove type prefixes (like 'int ' or 'float ') if present
        if isinstance(text, str):
            text = re.sub(r'^(int|float|str) ', '', text)

            # Check if this is a variable reference that wasn't replaced
            if text.startswith('$') and len(text) > 1:
                # Try to get the variable value from the interpreter
                var_name = text[1:]
                if var_name in self.parent.variables:
                    text = str(self.parent.variables[var_name])

        print(text)

    # run a command with python
    @pause_wrapper
    def python_(self, code: str):
        try:
            exec(code.replace("controls", "pyautogui"))
        except Exception as e:
            self.error("Python Code Execution Error", str(e))

    # run a macro from a file
    @pause_wrapper
    def run_file_(self, file_path):
        """Run a macro from a text file

        This command loads and executes a macro from a text file.
        It's useful for running saved macros or breaking up complex macros into smaller files.

        Usage:
            run_file path/to/macro.txt
            run_file $variable_with_path
        """
        if not file_path:
            self.error("Run File Error", "No file path provided")
            return

        # Remove quotes if they're surrounding the file path
        if (file_path.startswith('"') and file_path.endswith('"')) or \
           (file_path.startswith('\'') and file_path.endswith('\'')):
            file_path = file_path[1:-1]

        try:
            # Check if file exists
            if not os.path.exists(file_path):
                self.error("Run File Error", f"File not found: {file_path}")
                return

            # Read the file content
            with open(file_path, 'r') as file:
                macro_content = file.read()

            # Print a message indicating which file is being run
            print(f"Running macro from file: {file_path}")

            # Execute the macro content
            # We need to access the parent (interpreter) to parse the macro
            self.parent.full_parse(macro_content)

            # Print a message indicating the file execution is complete
            print(f"Completed running: {file_path}")

        except Exception as e:
            self.error("Run File Error", str(e))

    # change the macro execution speed
    def speed(self, args: list[str]):
        """Change the macro execution speed

        This command changes the delay between PyAutoGUI actions.
        Lower values make macros run faster, higher values make them slower.

        Usage:
            speed 0.1      # Set speed to 0.1 seconds delay (default)
            speed 0        # Set speed to 0 (no delay, fastest)
            speed 0.5      # Set speed to 0.5 seconds delay (slower)
            speed fast     # Set speed to 0 (no delay)
            speed slow     # Set speed to 0.5 seconds delay
            speed normal   # Set speed to 0.1 seconds delay (default)
        """
        if not args:
            # Show current speed
            print(f"Current macro speed: {pyautogui.PAUSE} seconds delay")
            return

        speed_value = args[0]

        # Handle preset speed values
        if speed_value.lower() == "fast":
            pyautogui.PAUSE = 0
            print("Macro speed set to FAST (0 seconds delay)")
        elif speed_value.lower() == "slow":
            pyautogui.PAUSE = 0.5
            print("Macro speed set to SLOW (0.5 seconds delay)")
        elif speed_value.lower() in ["normal", "default"]:
            pyautogui.PAUSE = 0.1
            print("Macro speed set to NORMAL (0.1 seconds delay)")
        else:
            # Try to parse as a number
            speed_float = self.numberfo(speed_value)
            if speed_float is not False and len(speed_float) > 0:
                new_speed = speed_float[0]
                if new_speed < 0:
                    self.error("Speed Command Error", "Speed cannot be negative")
                    return
                pyautogui.PAUSE = new_speed
                print(f"Macro speed set to {new_speed} seconds delay")
            else:
                self.error("Speed Command Error", "Please enter a valid number or use 'fast', 'slow', or 'normal'")