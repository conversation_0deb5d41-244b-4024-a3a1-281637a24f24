import re
import os
import pyautogui
from running.functions import Functions
from running.executer import Executer
from typing import Optional

class Interpreter:
    def __init__(self, pause: float = 0.0) -> None:
        self.executer = Executer(self, pause)
        self.bultiin_functions = Functions()

        # Get the actual methods from the executer
        self.command_map: dict = {}

        # Add methods that exist in the executer
        for method_name in dir(self.executer):
            # Skip private methods and non-callable attributes
            if method_name.startswith('_') or not callable(getattr(self.executer, method_name)):
                continue

            # Handle methods that end with underscore (like print_)
            if method_name.endswith('_'):
                command_name = method_name[:-1]  # Remove the trailing underscore
                self.command_map[command_name] = getattr(self.executer, method_name)
            else:
                self.command_map[method_name] = getattr(self.executer, method_name)

        # Add aliases and special commands
        self.command_map["copy"] = lambda _: pyautogui.hotkey("ctrl", "c")
        self.command_map["paste"] = lambda _: pyautogui.hotkey("ctrl", "v")
        self.command_map["moveto"] = self.executer.move  # Alias for move
        self.command_map["run"] = self.executer.run_file_  # Alias for run_file
        self.command_map["browser"] = self.executer.url  # Alias for url

        self.variables: dict = {}
        self.functions: dict = {}

    def parse_line(self, code: str) -> Optional[str]:
        # Skip empty lines or comments
        if not code.strip() or code.strip().startswith('#'):
            return None

        # Handle return statements in functions
        if code.strip().startswith('return '):
            # This is handled separately in the function execution
            # Just skip it here to avoid "Unknown command" error
            return None

        # Handle if statements in functions
        if code.strip().startswith('if '):
            # Extract the condition
            condition = code.strip()[3:].strip()
            # Remove trailing colon if present
            if condition.endswith(':'):
                condition = condition[:-1].strip()
            # Replace variables in the condition
            condition = self.replace_variables(condition)

            # Evaluate the condition
            try:
                # Handle different comparison operators
                if '==' in condition:
                    left, right = condition.split('==', 1)
                    left = left.strip()
                    right = right.strip()
                    # Try to convert to numbers if possible
                    try:
                        left = float(left)
                    except:
                        # Remove quotes if present for strings
                        if (left.startswith('"') and left.endswith('"')) or \
                           (left.startswith("'") and left.endswith("'")):
                            left = left[1:-1]

                    try:
                        right = float(right)
                    except:
                        # Remove quotes if present for strings
                        if (right.startswith('"') and right.endswith('"')) or \
                           (right.startswith("'") and right.endswith("'")):
                            right = right[1:-1]

                    result = (left == right)
                elif '>' in condition:
                    left, right = condition.split('>', 1)
                    left = left.strip()
                    right = right.strip()
                    # Convert to numbers
                    try:
                        # Try to convert left side to number
                        if isinstance(left, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in left:
                                left = left.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if left.replace('.', '', 1).isdigit() and left.count('.') <= 1:
                                left = float(left)
                                # If it's an integer, convert to int
                                if left.is_integer():
                                    left = int(left)

                        # Try to convert right side to number
                        if isinstance(right, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in right:
                                right = right.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if right.replace('.', '', 1).isdigit() and right.count('.') <= 1:
                                right = float(right)
                                # If it's an integer, convert to int
                                if right.is_integer():
                                    right = int(right)

                        # Ensure both are numbers before comparison
                        if not (isinstance(left, (int, float)) and isinstance(right, (int, float))):
                            raise ValueError(f"Both sides of comparison must be numbers")

                        result = (left > right)
                    except Exception as e:
                        self.error("Condition Error", f"Comparison > requires numeric values: {str(e)}")
                        result = False
                elif '<' in condition:
                    left, right = condition.split('<', 1)
                    left = left.strip()
                    right = right.strip()
                    # Convert to numbers
                    try:
                        # Try to convert left side to number
                        if isinstance(left, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in left:
                                left = left.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if left.replace('.', '', 1).isdigit() and left.count('.') <= 1:
                                left = float(left)
                                # If it's an integer, convert to int
                                if left.is_integer():
                                    left = int(left)

                        # Try to convert right side to number
                        if isinstance(right, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in right:
                                right = right.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if right.replace('.', '', 1).isdigit() and right.count('.') <= 1:
                                right = float(right)
                                # If it's an integer, convert to int
                                if right.is_integer():
                                    right = int(right)

                        # Ensure both are numbers before comparison
                        if not (isinstance(left, (int, float)) and isinstance(right, (int, float))):
                            raise ValueError("Both sides of comparison must be numbers")

                        result = (left < right)
                    except Exception as e:
                        self.error("Condition Error", f"Comparison < requires numeric values: {str(e)}")
                        result = False
                elif '>=' in condition:
                    left, right = condition.split('>=', 1)
                    left = left.strip()
                    right = right.strip()
                    # Convert to numbers
                    try:
                        # Try to convert left side to number
                        if isinstance(left, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in left:
                                left = left.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if left.replace('.', '', 1).isdigit() and left.count('.') <= 1:
                                left = float(left)
                                # If it's an integer, convert to int
                                if left.is_integer():
                                    left = int(left)

                        # Try to convert right side to number
                        if isinstance(right, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in right:
                                right = right.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if right.replace('.', '', 1).isdigit() and right.count('.') <= 1:
                                right = float(right)
                                # If it's an integer, convert to int
                                if right.is_integer():
                                    right = int(right)

                        # Ensure both are numbers before comparison
                        if not (isinstance(left, (int, float)) and isinstance(right, (int, float))):
                            raise ValueError("Both sides of comparison must be numbers")

                        result = (left >= right)
                    except Exception as e:
                        self.error("Condition Error", f"Comparison >= requires numeric values: {str(e)}")
                        result = False
                elif '<=' in condition:
                    left, right = condition.split('<=', 1)
                    left = left.strip()
                    right = right.strip()
                    # Convert to numbers
                    try:
                        # Try to convert left side to number
                        if isinstance(left, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in left:
                                left = left.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if left.replace('.', '', 1).isdigit() and left.count('.') <= 1:
                                left = float(left)
                                # If it's an integer, convert to int
                                if left.is_integer():
                                    left = int(left)

                        # Try to convert right side to number
                        if isinstance(right, str):
                            # Remove any trailing colon and text (which might be debug output)
                            if ':' in right:
                                right = right.split(':', 1)[0].strip()

                            # Check if it's a numeric string
                            if right.replace('.', '', 1).isdigit() and right.count('.') <= 1:
                                right = float(right)
                                # If it's an integer, convert to int
                                if right.is_integer():
                                    right = int(right)

                        # Ensure both are numbers before comparison
                        if not (isinstance(left, (int, float)) and isinstance(right, (int, float))):
                            raise ValueError("Both sides of comparison must be numbers")

                        result = (left <= right)
                    except Exception as e:
                        self.error("Condition Error", f"Comparison <= requires numeric values: {str(e)}")
                        result = False
                else:
                    # For other conditions, try a safer approach than eval
                    self.error("Condition Error", "Unsupported condition type. Use ==, >, <, >=, or <=")
                    result = False

                # Store the condition result for the next line
                self.last_if_result = bool(result)
            except Exception as e:
                self.error("Condition Error", f"Error evaluating condition: {e}")
                self.last_if_result = False
            return None

        # Skip line if the last if condition was false
        if hasattr(self, 'last_if_result') and not self.last_if_result and not code.strip().startswith(('if ', 'else:', 'elif ')):
            return None

        # Handle imports
        if code.strip().startswith(('import ', 'from ')):
            if code.strip().startswith('from '):
                # Handle "from random import strings"
                parts = code.strip()[5:].split(' import ')
                if len(parts) == 2:
                    base_module = parts[0].strip()
                    import_what = parts[1].strip()

                    # For "from random import strings", we need to import "random.strings"
                    if import_what != '*':
                        for item in [x.strip() for x in import_what.split(',')]:
                            full_module_path = f"{base_module}.{item}"
                            if not self.bultiin_functions.import_module(full_module_path):
                                self.error("Import Error", f"Failed to import module: {full_module_path}")
                                return None
                    else:
                        # Handle "from random.strings import *"
                        if not self.bultiin_functions.import_module(base_module):
                            self.error("Import Error", f"Failed to import module: {base_module}")
                            return None
            else:
                # Handle regular "import module.submodule"
                module_path = code.strip()[7:].strip()
                if not self.bultiin_functions.import_module(module_path):
                    self.error("Import Error", f"Failed to import module: {module_path}")
                    return None
            return None  # Important: return here after handling imports

        # Replace variables with their values
        code = self.replace_variables(code)

        # Check if this is a function call with the new syntax: $function_name(args)
        if code.startswith('$'):
            function_call_pattern = r'\$(\w+)\(([^)]*)\)'
            function_match = re.match(function_call_pattern, code)

            if function_match:
                function_name = function_match.group(1)
                args_str = function_match.group(2)

                # Check if this is a user-defined function
                if function_name in self.functions:
                    function_info = self.functions[function_name]
                    expected_args = function_info["arg_count"]
                    arg_names = function_info.get("args", [])

                    # Parse the arguments
                    actual_args = []
                    if args_str.strip():
                        # Use regex to parse arguments, keeping quoted strings intact
                        pattern = r'''(?:[^,"']|"[^"]*"|'[^']*')+'''
                        matches = re.findall(pattern, args_str)
                        actual_args = [match.strip() for match in matches]

                    # Validate argument count
                    if len(actual_args) != expected_args:
                        self.error("Function Call Error",
                                  f"Function '{function_name}' expects {expected_args} arguments, but got {len(actual_args)}")
                        return None

                    # Process the arguments (replace variables, etc.)
                    processed_args = []
                    for arg in actual_args:
                        # Replace variables in the argument
                        processed_arg = self.replace_variables(arg)
                        processed_args.append(processed_arg)

                    # Create temporary variables for the function arguments
                    temp_vars = {}
                    for arg_name, arg_value in zip(arg_names, processed_args):
                        temp_vars[arg_name] = arg_value

                    # Save current variables and set function arguments
                    saved_vars = self.variables.copy()
                    self.variables.update(temp_vars)

                    # Process each line of the function body
                    function_body = function_info["body"]

                    for line in function_body.splitlines():
                        if line.strip():
                            # Skip return statements - they're handled separately
                            if line.strip().startswith('return '):
                                continue

                            # Process the line
                            self.parse_line(line)

                    # Handle return value if present
                    return_value = None
                    if function_info.get("return_value"):
                        return_expr = function_info["return_value"]

                        # Check if the return value is a variable reference
                        if return_expr.startswith('$'):
                            var_name = return_expr[1:]
                            if var_name in self.variables:
                                # It's a variable, get its value
                                return_value = self.variables[var_name]
                            else:
                                # It's not a recognized variable, try to process it
                                return_value = self.replace_variables(return_expr)

                                # Check if it's a function call
                                function_call_pattern = r'\$(\w+)\.(\w+)\(([^)]*)\)'
                                function_match = re.match(function_call_pattern, return_value)

                                if function_match:
                                    # It's a module function call
                                    module_name = function_match.group(1)
                                    func_name = function_match.group(2)
                                    args_str = function_match.group(3)

                                    # Parse arguments
                                    args = []
                                    if args_str.strip():
                                        # Split by comma but handle whitespace better
                                        args = [arg.strip() for arg in args_str.split(',')]

                                    # Call module function
                                    try:
                                        result = self.bultiin_functions.call_module_function(module_name, func_name, *args)
                                        return_value = result
                                    except Exception as e:
                                        self.error("Module Function Error", str(e))
                                        return_value = None
                        else:
                            # It's a simple expression, just replace variables
                            return_value = self.replace_variables(return_expr)

                    # Restore original variables
                    self.variables = saved_vars

                    # Always return None to avoid "Unknown command" errors
                    # This makes standalone function calls work like in Python
                    return None

                # If not a user-defined function, try module function
                else:
                    # Check if it's a module function call (contains a dot)
                    if '.' in function_name:
                        module_name, func_name = function_name.rsplit('.', 1)

                        # Parse arguments
                        args = []
                        if args_str.strip():
                            # Split by comma but handle whitespace better
                            args = [arg.strip() for arg in args_str.split(',')]

                        # Call module function
                        try:
                            result = self.bultiin_functions.call_module_function(module_name, func_name, *args)
                            # Always return None for standalone function calls to avoid "Unknown command" errors
                            return None
                        except Exception as e:
                            self.error("Module Function Error", str(e))
                            return None
                    else:
                        self.error("Function Error", f"Function '{function_name}' not found")
                        return None

        # Check if this is a variable definition
        var_def = self.parse_variable_definition(code)
        if var_def:
            var_names, values = var_def

            # Process function calls in values
            processed_values = []
            for value in values:
                # Check if the value is a function call
                if value.startswith('$'):
                    # Check if it's a module function call
                    module_function_pattern = r'\$(\w+)\.(\w+)\(([^)]*)\)'
                    module_match = re.match(module_function_pattern, value)

                    if module_match:
                        # It's a module function call
                        module_name = module_match.group(1)
                        func_name = module_match.group(2)
                        args_str = module_match.group(3)

                        # Parse arguments
                        args = []
                        if args_str.strip():
                            # First, replace any variables in the arguments
                            processed_args_str = self.replace_variables(args_str)
                            # Split by comma but handle whitespace better
                            args = [arg.strip() for arg in processed_args_str.split(',')]

                        # Call module function
                        try:
                            result = self.bultiin_functions.call_module_function(module_name, func_name, *args)
                            processed_values.append(result)
                        except Exception as e:
                            self.error("Module Function Error", str(e))
                            processed_values.append(value)
                    else:
                        # Check if it's a user-defined function call
                        function_call_pattern = r'\$(\w+)\(([^)]*)\)'
                        function_match = re.match(function_call_pattern, value)

                        if function_match:
                            function_name = function_match.group(1)

                            # Call the function and use its return value
                            function_result = self.parse_line(value)
                            processed_values.append(function_result)
                        else:
                            # It's a variable reference
                            var_name = value[1:]
                            if var_name in self.variables:
                                processed_values.append(self.variables[var_name])
                            else:
                                processed_values.append(value)
                else:
                    processed_values.append(value)

            # Assign the variables
            for i, name in enumerate(var_names):
                self.variables[name] = processed_values[i]

            return None

        # Check if this is a command
        first_word = code.split()[0].strip() if code else ""
        if first_word in self.command_map:
            cmd = first_word
            repeat_count = 1
            args = ""
            try:
                if len(code.split()) > 1 and code.split()[1].strip().startswith("*"):
                    repeat_str = code.split()[1].strip()[1:]
                    repeat_count = int(repeat_str)
                    args = code[len(cmd) + len(repeat_str) + 2:]
                else:
                    args = code[len(cmd)+1:]
            except KeyError:
                pass
            for i in range(repeat_count):
                result = self.command_map[cmd](args)
                if result is not None:
                    return False # type: ignore
            return None

        # Check if this is a standalone function call (starts with $)
        if code.startswith('$'):
            # This is likely a function call that we've already processed
            # Just return None to indicate success
            return None

        # If we get here, the command is not recognized
        if code:
            self.error("Command Error", f"Unknown command '{first_word}'")
            return None

    def error(self, title: str, content: str) -> None:
        print(f"{title} on line {self.current_line}: {content}")

    # replace variables with their value
    def replace_variables(self, code: str) -> str:
        import re

        # First, check for nested function calls
        if '$' in code and ('(' in code and ')' in code):
            # Process function calls from innermost to outermost
            code = self.process_nested_functions(code)

        # Process the code in multiple passes to handle nested function calls
        # This ensures that variables inside nested function calls are replaced correctly

        # First pass: Replace simple variables that aren't in function calls
        # This regex finds standalone variables like $a but not $basic.add
        var_pattern = r'\$(\w+)(?!\.|\()'

        # Find all variable references
        var_refs = re.findall(var_pattern, code)

        # Replace variables with their values
        processed_code = code
        for var_name in var_refs:
            if var_name in self.variables:
                # Use regex to ensure we only replace standalone variables
                # This will match $var but not $variable or $var_name
                exact_var_pattern = r'\${0}(?!\w|\.|\()'.format(re.escape(var_name))
                processed_code = re.sub(exact_var_pattern, str(self.variables[var_name]), processed_code)
            else:
                # If the variable doesn't exist, leave it as is
                # This will be handled by the error handling in the math functions
                pass

        return processed_code


    def process_nested_functions(self, code: str, depth=0):
        """Process nested function calls from innermost to outermost"""
        import re

        # Prevent infinite recursion
        if depth > 100:
            self.error("Recursion Error", "Maximum recursion depth exceeded")
            return "Error: Maximum recursion depth exceeded"

        # Keep processing until no more nested functions are found
        max_iterations = 20  # Prevent infinite loops
        iteration = 0

        while '$' in code and '(' in code and iteration < max_iterations:
            iteration += 1

            # Find the innermost function call (one without nested $ inside its arguments)
            # First, try to find module function calls like $basic.add(...)
            module_pattern = r'\$(\w+)\.(\w+)\(([^$()]*)\)'
            match = re.search(module_pattern, code)


            if not match:
                # If no module function call found, try to find user function calls like $myFunc(...)
                user_pattern = r'\$(\w+)\(([^$()]*)\)'
                match = re.search(user_pattern, code)


            if not match:
                # No more function calls without nested calls inside
                # Look for any function call that might have nested calls
                # This pattern will match any function call, including those with nested calls
                any_pattern = r'\$(\w+(?:\.\w+)?)\(([^()]*)\)'
                match = re.search(any_pattern, code)


                if match:
                    # Found a function call with potential nested calls
                    # Process its arguments recursively
                    full_match = match.group(0)
                    function_name = match.group(1)
                    args_str = match.group(2)

                    # Process nested calls in arguments
                    processed_args = self.process_nested_functions(args_str, depth + 1)

                    # Reconstruct the function call with processed arguments
                    new_call = f"${function_name}({processed_args})"
                    code = code.replace(full_match, new_call, 1)
                    continue
                else:
                    # No more function calls found
                    break

            # Process the found function call
            full_match = match.group(0)  # The entire matched function call

            # Check if it's a module function call or user function call
            if len(match.groups()) >= 3 and match.group(2) is not None:
                # Module function call
                module_name = match.group(1)
                func_name = match.group(2)
                args_str = match.group(3)

                # Process the function call
                result = self.process_module_function_call(module_name, func_name, args_str)

            else:
                # User function call
                function_name = match.group(1)
                args_str = match.group(2)

                # Process the function call
                result = self.process_user_function_call(function_name, args_str)

            # Replace the function call with its result
            code = code.replace(full_match, str(result), 1)

        return code

    def process_module_function_call(self, module_name: str, func_name: str, args_str: str) -> str:
        """Process a module function call and return its result"""
        # Parse arguments
        args = []
        if args_str.strip():
            # Check if there are nested parentheses that might cause issues
            if '(' in args_str and ')' in args_str:
                print(f"DEBUG: Found nested parentheses in arguments: {args_str}")
                # This is a more complex case, we need to handle it differently
                # For now, let's just use the arguments as is
                args = [args_str]
            else:
                # Split by comma but handle whitespace better
                args = [arg.strip() for arg in args_str.split(',')]

        # Process each argument (replace variables)
        processed_args = []
        for arg in args:
            # Replace variables in the argument
            processed_arg = self.replace_variables(arg)
            processed_args.append(processed_arg)

        # Call the module function
        try:
            result = self.bultiin_functions.call_module_function(module_name, func_name, *processed_args)
            return result
        except Exception as e:
            self.error("Module Function Error", str(e))
            return f"Error: {str(e)}"

    def process_user_function_call(self, function_name: str, args_str: str) -> str:
        """Process a user function call and return its result"""
        # Check if the function exists
        if function_name not in self.functions:
            self.error("Function Error", f"Function '{function_name}' not found")
            return f"Error: Function {function_name} not found"

        function_info = self.functions[function_name]
        expected_args = function_info["arg_count"]
        arg_names = function_info.get("args", [])

        # Parse arguments
        actual_args = []
        if args_str.strip():
            # Split by comma but handle whitespace better
            actual_args = [arg.strip() for arg in args_str.split(',')]

        # Validate argument count
        if len(actual_args) != expected_args:
            self.error("Function Call Error",
                      f"Function '{function_name}' expects {expected_args} arguments, but got {len(actual_args)}")
            return f"Error: Function {function_name} argument count mismatch"

        # Process arguments (replace variables)
        processed_args = []
        for arg in actual_args:
            # Replace variables in the argument
            processed_arg = self.replace_variables(arg)
            processed_args.append(processed_arg)

        # Create temporary variables for function arguments
        temp_vars = {}
        for arg_name, arg_value in zip(arg_names, processed_args):
            # Convert string arguments to appropriate types if possible
            if isinstance(arg_value, str):
                # Try to convert to number if it looks like a number
                if arg_value.replace('.', '', 1).isdigit() and arg_value.count('.') <= 1:
                    try:
                        if '.' in arg_value:
                            arg_value = float(arg_value)
                        else:
                            arg_value = int(arg_value)
                    except (ValueError, TypeError):
                        pass
            temp_vars[arg_name] = arg_value

        # Save current variables and set function arguments
        saved_vars = self.variables.copy()
        self.variables.update(temp_vars)

        # Execute function body
        return_value = None
        function_body = function_info["body"]

        for line in function_body.splitlines():
            if line.strip():
                # Replace variable references in the line
                processed_line = line

                # Replace $param with the actual value
                for arg_name, arg_value in temp_vars.items():
                    # Use regex to ensure we only replace standalone variables
                    var_pattern = r'\$' + re.escape(arg_name) + r'(?!\w|\.|\()'
                    processed_line = re.sub(var_pattern, str(arg_value), processed_line)

                if processed_line.strip().startswith('return '):
                    # Process return statement
                    return_expr = processed_line.strip()[7:].strip()
                    return_value = self.replace_variables(return_expr)
                    break
                else:
                    # Execute the line
                    self.parse_line(processed_line)

        # If no return statement was found in the body, check if there's a return value in the function info
        if return_value is None and function_info.get("return_value"):
            return_expr = function_info["return_value"]

            # Replace variable references in the return expression
            processed_expr = return_expr
            for arg_name, arg_value in temp_vars.items():
                # Use regex to ensure we only replace standalone variables
                var_pattern = r'\$' + re.escape(arg_name) + r'(?!\w|\.|\()'
                processed_expr = re.sub(var_pattern, str(arg_value), processed_expr)

            return_value = self.replace_variables(processed_expr)

        # Restore original variables
        self.variables = saved_vars

        return return_value # type: ignore

    def parse_variable_definition(self, s: str) -> tuple[list[str], list[str]]:
        # Check for multiple assignment (e.g., x1, y1 = 0, 0)
        multi_match = re.match(r'\s*([a-zA-Z_]\w*(?:\s*,\s*[a-zA-Z_]\w*)*)\s*=\s*(.+)', s)
        if multi_match:
            # Get the left side (variable names) and right side (values)
            left_side = multi_match.group(1)
            right_side = multi_match.group(2)

            # Split the variable names and values
            var_names = [name.strip() for name in left_side.split(',')]

            # For multiple variables, we expect comma-separated values
            if len(var_names) > 1:
                # Use regex to parse values, keeping quoted strings intact
                pattern = r'''(?:[^,"']|"[^"]*"|'[^']*')+'''
                matches = re.findall(pattern, right_side)
                values = [match.strip() for match in matches]

                # Check if the number of variables matches the number of values
                if len(var_names) == len(values):
                    return var_names, values
                else:
                    self.error("Variable Assignment Error",
                              f"Number of variables ({len(var_names)}) doesn't match number of values ({len(values)})")
                    return None # type: ignore
            else:
                # For single variable assignment, use the entire right side as the value
                # This handles function calls with commas like $basic.add($a, $b)
                return var_names, [right_side.strip()]

        # Regular single variable assignment
        match = re.match(r'\s*([a-zA-Z_]\w*)\s*=\s*(.+)', s)
        if match:
            var_name = match.group(1)
            value = match.group(2).strip()
            return [var_name], [value]

        return None # type: ignore

    def parse_function_definitions(self, code: str) -> tuple[dict, str]:
        # Syntax: func name(arg1, arg2, ...):
        #             body

        # First, find all function definitions
        func_matches = re.finditer(r'func\s+(\w+)\(([^)]*)\)\s*:', code)
        functions = {}

        # Process each function definition
        for match in func_matches:
            func_start = match.start()
            func_name = match.group(1)
            args_str = match.group(2)

            # Find the end of the function
            # This is more reliable than using regex to capture the body
            lines = code[func_start:].splitlines()
            body_lines = []
            in_function = False

            for i, line in enumerate(lines):
                if i == 0:
                    # This is the function definition line
                    in_function = True
                    continue

                # Check if this line starts a new function or is outside the indentation
                if line.strip().startswith('func ') or (line.strip() and not line.startswith('    ')):
                    break

                if in_function and line.strip():
                    body_lines.append(line.strip())

            # Join the body lines
            body_text = '\n'.join(body_lines)

            # Extract return statement if present
            return_value = None
            body = []

            for line in body_lines:
                if line.startswith('return '):
                    # Extract the return value expression
                    return_value = line[7:].strip()  # Remove 'return ' prefix
                else:
                    body.append(line)

            # Join the body lines back together
            body_text = '\n'.join(body)

            # Parse arguments
            args = [arg.strip() for arg in args_str.split(',') if arg.strip()]
            arg_count = len(args)

            # Store function definition
            functions[func_name] = {
                "arg_count": arg_count,
                "args": args,  # Store argument names
                "body": body_text,
                "return_value": return_value
            }

        # Remove function definitions from the code
        cleaned_code = re.sub(r'func\s+\w+\([^)]*\)\s*:[\s\S]*?(?=\n\s*(?:func\s|$|[^\s]))', '', code).strip()

        return functions, cleaned_code

    def full_parse(self, code: str) -> None:
        self.functions, code = self.parse_function_definitions(code)
        self.current_line = 0

        # Process each line
        lines = code.splitlines()
        for i in range(len(lines)):
            line = lines[i]
            self.current_line = i + 1

            if not line.strip() or line.strip().startswith('#'):
                continue

            # Special handling for standalone function calls
            if line.strip().startswith('$'):
                # This is a function call - process it but don't try to interpret the result as a command
                function_call_pattern = r'\$(\w+)\(([^)]*)\)'
                function_match = re.match(function_call_pattern, line.strip())

                if function_match:
                    function_name = function_match.group(1)
                    args_str = function_match.group(2)

                    # Process the function call
                    if function_name in self.functions:
                        # It's a user-defined function
                        self.process_user_function_call(function_name, args_str)
                    elif '.' in function_name:
                        # It's a module function call
                        module_name, func_name = function_name.rsplit('.', 1)
                        args = [arg.strip() for arg in args_str.split(',') if arg.strip()]
                        try:
                            self.bultiin_functions.call_module_function(module_name, func_name, *args)
                        except Exception as e:
                            self.error("Module Function Error", str(e))
                    else:
                        self.error("Function Error", f"Function '{function_name}' not found")
            else:
                # Regular command or variable assignment
                self.parse_line(line)

    def kill(self) -> None:
        self.executer.kill() # type: ignore
        os._exit(1)
