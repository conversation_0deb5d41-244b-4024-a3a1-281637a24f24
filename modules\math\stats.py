"""
Statistical operations module.
"""
import math
from collections import Counter

MODULE_NAME = "math.stats"

def register_functions():
    return {
        'mean': mean,
        'median': median,
        'mode': mode,
        'range': range_value,
        'variance': variance,
        'std_dev': standard_deviation,
        'percentile': percentile,
        'correlation': correlation,
        'z_score': z_score,
        'min': minimum,
        'max': maximum,
        'sum': sum_values,
        'count': count_values
    }

def parse_list(data_str):
    """Parse a comma-separated string into a list of floats"""
    if isinstance(data_str, str):
        return [float(x.strip()) for x in data_str.split(',') if x.strip()]
    return [float(data_str)]

def mean(data):
    """Calculate the arithmetic mean (average) of a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    return sum(data_list) / len(data_list)

def median(data):
    """Calculate the median (middle value) of a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    sorted_data = sorted(data_list)
    n = len(sorted_data)
    
    if n % 2 == 0:
        # Even number of elements
        return (sorted_data[n//2 - 1] + sorted_data[n//2]) / 2
    else:
        # Odd number of elements
        return sorted_data[n//2]

def mode(data):
    """Find the mode (most common value) in a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    # Count occurrences of each value
    counter = Counter(data_list)
    max_count = max(counter.values())
    
    # Find all values that occur max_count times
    modes = [k for k, v in counter.items() if v == max_count]
    
    if len(modes) == len(data_list):
        return "No unique mode found"
    
    return modes[0] if len(modes) == 1 else modes

def range_value(data):
    """Calculate the range (difference between max and min) of a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    return max(data_list) - min(data_list)

def variance(data):
    """Calculate the variance of a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    if len(data_list) == 1:
        return 0
    
    avg = mean(data)
    return sum((x - avg) ** 2 for x in data_list) / len(data_list)

def standard_deviation(data):
    """Calculate the standard deviation of a list of numbers"""
    var = variance(data)
    if isinstance(var, str):  # Error message
        return var
    return math.sqrt(var)

def percentile(data, p):
    """Calculate the pth percentile of a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    p = float(p)
    if p < 0 or p > 100:
        return "Error: Percentile must be between 0 and 100"
    
    sorted_data = sorted(data_list)
    n = len(sorted_data)
    
    if p == 0:
        return sorted_data[0]
    if p == 100:
        return sorted_data[-1]
    
    # Calculate the index
    idx = (n - 1) * p / 100
    idx_floor = math.floor(idx)
    idx_ceil = math.ceil(idx)
    
    if idx_floor == idx_ceil:
        return sorted_data[int(idx)]
    
    # Interpolate between the two values
    lower_value = sorted_data[idx_floor]
    upper_value = sorted_data[idx_ceil]
    fraction = idx - idx_floor
    
    return lower_value + (upper_value - lower_value) * fraction

def correlation(data_x, data_y):
    """Calculate the Pearson correlation coefficient between two datasets"""
    x_list = parse_list(data_x)
    y_list = parse_list(data_y)
    
    if not x_list or not y_list:
        return "Error: Empty dataset"
    
    if len(x_list) != len(y_list):
        return "Error: Datasets must have the same length"
    
    n = len(x_list)
    if n <= 1:
        return "Error: Need at least two data points"
    
    # Calculate means
    mean_x = sum(x_list) / n
    mean_y = sum(y_list) / n
    
    # Calculate covariance and standard deviations
    covariance = sum((x - mean_x) * (y - mean_y) for x, y in zip(x_list, y_list)) / n
    std_dev_x = math.sqrt(sum((x - mean_x) ** 2 for x in x_list) / n)
    std_dev_y = math.sqrt(sum((y - mean_y) ** 2 for y in y_list) / n)
    
    if std_dev_x == 0 or std_dev_y == 0:
        return "Error: Standard deviation is zero"
    
    return covariance / (std_dev_x * std_dev_y)

def z_score(value, data):
    """Calculate the z-score (standard score) of a value in a dataset"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    avg = mean(data)
    std_dev = standard_deviation(data)
    
    if isinstance(std_dev, str) or std_dev == 0:  # Error message or zero std dev
        return "Error: Cannot calculate z-score (standard deviation is zero)"
    
    return (float(value) - avg) / std_dev

def minimum(data):
    """Find the minimum value in a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    return min(data_list)

def maximum(data):
    """Find the maximum value in a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    return max(data_list)

def sum_values(data):
    """Calculate the sum of a list of numbers"""
    data_list = parse_list(data)
    if not data_list:
        return "Error: Empty dataset"
    
    return sum(data_list)

def count_values(data):
    """Count the number of values in a list"""
    data_list = parse_list(data)
    return len(data_list)
